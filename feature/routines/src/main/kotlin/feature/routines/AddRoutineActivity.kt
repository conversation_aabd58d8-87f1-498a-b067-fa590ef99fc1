package feature.routines

import android.app.Activity
import android.os.Bundle
import android.view.View
import androidx.appcompat.app.AppCompatActivity
import androidx.core.view.isVisible
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import data.network.android.RoutineModel
import data.network.android.models.CreateRoutineRequest
import data.network.android.models.TimeRoutineModel
import data.network.android.models.TimeSlot
import feature.common.dialogs.OnActionYesNo
import feature.common.dialogs.ProgressDialogUtils
import feature.common.dialogs.dialogYesNo
import data.utils.android.settings.SharedPreferenceUtils
import data.utils.android.toast
import keyless.feature.routines.databinding.ActivityAddRoutineBinding
import java.text.SimpleDateFormat
import java.util.Date

class AddRoutineActivity : AppCompatActivity(), AdapterAddRoutines.ClickOnRoutines {

    private var routineId: String = ""
    lateinit var adapterAddRoutines: AdapterAddRoutines
    private var dataList: ArrayList<TimeRoutineModel> = ArrayList()
    lateinit var mViewModel: RoutineViewModel
    private val sharePrefs: SharedPreferenceUtils by lazy {
        SharedPreferenceUtils.getInstance(
            this
        )
    }
    private var modelRoutineFromScreen: RoutineModel = RoutineModel()
    private lateinit var binding: ActivityAddRoutineBinding

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityAddRoutineBinding.inflate(layoutInflater)
        setContentView(binding.root)
        initz()
        setRecyclerView()
        clickListeners()
        observerInit()
    }

    private fun observerInit() {
        mViewModel.progress.observe(this) {
            if (it) {
                ProgressDialogUtils.getInstance().hideProgress()
                ProgressDialogUtils.getInstance().showProgress(this, true)
            } else {
                ProgressDialogUtils.getInstance().hideProgress()
            }
        }

        mViewModel.error.observe(this) {
            toast(it)
        }
    }

    private fun initz() {
        mViewModel = ViewModelProvider(this)[RoutineViewModel::class.java]
    }

    private fun clickListeners() {
        binding.deleteRoutine.setOnClickListener {
            showDeleteDialog(1, TimeRoutineModel(), -1)
        }

        binding.viewBack.setOnClickListener {
            finish()
        }
        binding.addMoreRoutine.setOnClickListener {
            val itemData = addRoutine()
            adapterAddRoutines.addItemAdapter(itemData, modelRoutineFromScreen)
        }

        binding.btnSaveEditDetails.setOnClickListener {
            var isStartTimeEmpty = false
            var isEndTimeEmpty = false
            var isDaysEmpty = false
            for (i in adapterAddRoutines.data) {
                if (i.time_slot.start_hour.isNullOrEmpty() || i.start_hour.isNullOrEmpty()) {
                    isStartTimeEmpty = true
                    break
                }
            }

            for (i in adapterAddRoutines.data) {
                if (i.time_slot.end_hour.isNullOrEmpty() || i.end_hour.isNullOrEmpty()) {
                    isEndTimeEmpty = true
                    break
                }
            }

            for (i in adapterAddRoutines.data) {
                if (i.allowed_days[0] != 1 &&
                    i.allowed_days[1] != 1 &&
                    i.allowed_days[2] != 1 &&
                    i.allowed_days[3] != 1 &&
                    i.allowed_days[4] != 1 &&
                    i.allowed_days[5] != 1 &&
                    i.allowed_days[6] != 1
                ) {
                    isDaysEmpty = true
                    break
                }
            }

            if (binding.etRoutine.text.toString().isEmpty()) {
                toast(getString(keyless.data.utils.android.R.string.please_enter_routine_name))
            } else if (adapterAddRoutines.data.isEmpty()) {
                toast(getString(keyless.data.utils.android.R.string.please_add_atleast_one_routine))
            } else if (isStartTimeEmpty) {
                toast(getString(keyless.data.utils.android.R.string.please_select_starting_time))
            } else if (isEndTimeEmpty) {
                toast(getString(keyless.data.utils.android.R.string.please_select_ending_time))
            } else if (isDaysEmpty) {
                toast(getString(keyless.data.utils.android.R.string.please_select_days))
            } else if (!isEndTime()) {
                toast(getString(keyless.data.utils.android.R.string.end_time_should_greater_start_time))
            } else {
                val request = CreateRoutineRequest()
                request.name = binding.etRoutine.text.toString()
                request.time_ranges = adapterAddRoutines.data
                if (!intent.hasExtra("model")) {
                    mViewModel.hitAddRoutineApi(request, sharePrefs.token).observe(this) {
                        setResult(Activity.RESULT_OK)
                        finish()
                    }
                } else {
                    mViewModel.hitEditRoutineApi(
                        modelRoutineFromScreen._id,
                        request,
                        sharePrefs.token
                    ).observe(this) {
                        setResult(Activity.RESULT_OK)
                        finish()
                    }
                }
            }
        }
    }

    private fun isEndTime(): Boolean {
        for (i in adapterAddRoutines.data) {
            val strStartTime = i.time_slot.start_hour + ":" + i.time_slot.start_min
            val strEndTime = i.time_slot.end_hour + ":" + i.time_slot.end_min

            val sdf = SimpleDateFormat("HH:mm")
            val inTime: Date = sdf.parse(strStartTime)
            val outTime: Date = sdf.parse(strEndTime)
            if (outTime.before(inTime)) {
                return false
            }
        }

        return true
    }

    private fun showDeleteDialog(fromWhere: Int, model: TimeRoutineModel, position: Int) {
        var msg = ""
        if (fromWhere == 1) {
            msg = getString(keyless.data.utils.android.R.string.do_you_want_delete_routine)
        } else {
            msg = getString(keyless.data.utils.android.R.string.do_you_delete_routine_slot)
        }
        dialogYesNo(
            this,
            "Delete",
            msg,
            object : OnActionYesNo {
                override fun onYes(view: View) {
                    if (fromWhere == 1) {
                        mViewModel.hitDeleteRoutine(sharePrefs.token, routineId)
                            .observe(
                                this@AddRoutineActivity
                            ) {
                                if (it.success) {
                                    setResult(Activity.RESULT_OK)
                                    finish()
                                }
                            }
                    } else {
                        if (model.id == "0") {
                            adapterAddRoutines.removeItem(position)
                        } else {
                            mViewModel.hitDeleteTimeRangeApi(
                                model.id,
                                sharePrefs.token
                            ).observe(this@AddRoutineActivity) {
                                if (it.success) {
                                    adapterAddRoutines.removeItem(position)
                                } else {
                                    toast(it.message)
                                }
                            }
                        }
                    }
                }

                override fun onNo(view: View) {
                }

                override fun onClickData(view: View, data: String) {
                }
            }
        )
    }

    private fun setRecyclerView() {
        binding.recyclerRoutine.layoutManager = LinearLayoutManager(this)
        adapterAddRoutines = AdapterAddRoutines(this)
        binding.recyclerRoutine.adapter = adapterAddRoutines
        if (intent.hasExtra("model")) {
            modelRoutineFromScreen = intent.getParcelableExtra("model")!!
            binding.btnSaveEditDetails.text = getString(keyless.feature.common.R.string.update)
            binding.txtHeading.text = getString(keyless.data.utils.android.R.string.update_routine)
            mViewModel.getEditRoutineData(modelRoutineFromScreen._id, sharePrefs.token).observe(
                this
            ) { it ->
                routineId = it.routine._id
                adapterAddRoutines.data.clear()
                binding.etRoutine.setText(it.routine.name)
                binding.deleteRoutine.isVisible = modelRoutineFromScreen.totalLocks <= 0
                it.time_ranges.forEach {
                    val timeRoutineModel = TimeRoutineModel()
                    timeRoutineModel.id = it._id
                    timeRoutineModel.allowed_days = it.allowed_days
                    timeRoutineModel.always_open = it.always_open
                    timeRoutineModel.holidays = it.holidays
                    timeRoutineModel.start_hour = it.time_slot.start_hour
                    timeRoutineModel.end_hour = it.time_slot.end_hour
                    timeRoutineModel.start_min = it.time_slot.start_min
                    timeRoutineModel.end_min = it.time_slot.end_min
                    val timeSlot = TimeSlot()
                    timeSlot.start_hour = it.time_slot.start_hour
                    timeSlot.end_min = it.time_slot.end_min
                    timeSlot.start_min = it.time_slot.start_min
                    timeSlot.end_hour = it.time_slot.end_hour
                    timeRoutineModel.time_slot = timeSlot
                    adapterAddRoutines.addItemAdapter(timeRoutineModel, modelRoutineFromScreen)
                }
            }
        } else {
            binding.deleteRoutine.isVisible = false
            binding.btnSaveEditDetails.text = getString(keyless.data.utils.android.R.string.btnSave)
            binding.txtHeading.text = getString(keyless.data.utils.android.R.string.add_routine)
            dataList = arrayListOf(addRoutine())
            adapterAddRoutines.updateAdapter(dataList)
        }
    }

    private fun addRoutine(): TimeRoutineModel {
        val timeRoutineModel = TimeRoutineModel()
        timeRoutineModel.id = "0"
        timeRoutineModel.allowed_days = arrayListOf(0, 0, 0, 0, 0, 0, 0)
        timeRoutineModel.always_open = false
        timeRoutineModel.holidays = false
        timeRoutineModel.start_hour = ""
        timeRoutineModel.end_hour = ""
        timeRoutineModel.start_min = ""
        timeRoutineModel.end_min = ""
        val timeSlot = TimeSlot()
        timeSlot.start_hour = ""
        timeSlot.end_min = ""
        timeSlot.start_min = ""
        timeSlot.end_hour = ""
        timeRoutineModel.time_slot = timeSlot
        return timeRoutineModel
    }

    override fun clickOnDelete(
        position: Int,
        model: TimeRoutineModel
    ) {
        showDeleteDialog(2, model, position)
    }
}