package feature.routines

import android.annotation.SuppressLint
import android.app.TimePickerDialog
import android.content.Context
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.view.isVisible
import androidx.recyclerview.widget.RecyclerView
import data.common.preferences.Preferences
import data.network.android.RoutineModel
import data.network.android.models.TimeRoutineModel
import data.utils.android.CommonValues
import data.utils.android.hideKeyboard
import keyless.feature.routines.R
import keyless.feature.routines.databinding.AddRoutineAdapterLayoutBinding
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Locale
import java.util.TimeZone


class AdapterAddRoutines(contextMain: Context) :
    RecyclerView.Adapter<AdapterAddRoutines.ViewHolder>() {


    var data: ArrayList<TimeRoutineModel> = ArrayList()
    var modelRoutine: RoutineModel = RoutineModel()
    lateinit var context: Context
    var listener = contextMain as ClickOnRoutines


    override fun onCreateViewHolder(viewGroup: ViewGroup, viewType: Int): ViewHolder {
        context = viewGroup.context
        val binding = AddRoutineAdapterLayoutBinding.inflate(LayoutInflater.from(context), viewGroup , false)
        return ViewHolder(binding)
    }

    override fun getItemCount() = data.size

    @SuppressLint("SetTextI18n")
    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        holder.bind(data[position] , position)

    }

    private fun setEndTime(endHour: String, endMin: String, position: Int) {
        if (data[position].id == "0") {
            data[position].end_hour = endHour
            data[position].end_min = endMin
            data[position].time_slot.end_hour = endHour
            data[position].time_slot.end_min = endMin
        } else {
            data[position].end_hour = ""
            data[position].end_min = ""
            data[position].end_hour = endHour
            data[position].end_min = endMin
            data[position].time_slot.end_hour = endHour
            data[position].time_slot.end_min = endMin
        }

        notifyDataSetChanged()

    }

    @SuppressLint("NotifyDataSetChanged")
    private fun setStartTime(startHour: String, startMin: String, position: Int) {
        if (data[position].id == "0") {
            data[position].start_hour = startHour
            data[position].start_min = startMin
            data[position].time_slot.start_hour = startHour
            data[position].time_slot.start_min = startMin
        } else {
            data[position].start_hour = ""
            data[position].start_min = ""
            data[position].start_hour = startHour
            data[position].start_min = startMin
            data[position].time_slot.start_hour = startHour
            data[position].time_slot.start_min = startMin
        }
        notifyDataSetChanged()

    }

    @SuppressLint("NotifyDataSetChanged")
    private fun clickDays(position: Int, day: Int) {
        if (data[position].allowed_days[day] == 1) {
            data[position].allowed_days[day] = 0
        } else {
            data[position].allowed_days[day] = 1
        }
        notifyDataSetChanged()
    }


    fun updateAdapter(dataList: ArrayList<TimeRoutineModel>) {
        data = dataList
        notifyDataSetChanged()

    }

    fun addItemAdapter(dataList: TimeRoutineModel, modelRoutineFromScreen: RoutineModel) {
        data.add(dataList)
        modelRoutine = modelRoutineFromScreen
        notifyDataSetChanged()

    }

    fun removeItem(position: Int) {
        data.removeAt(position)
        notifyDataSetChanged()
    }


    inner class ViewHolder(val binding: AddRoutineAdapterLayoutBinding)
        : RecyclerView.ViewHolder(binding.root){

        fun bind(model : TimeRoutineModel, position: Int){

            binding.txtMon.setOnClickListener {
                clickDays(position, 0)
            }

            binding.txtTues.setOnClickListener {
                clickDays(position, 1)
            }
            binding.txtWed.setOnClickListener {
                clickDays(position, 2)
            }
            binding.txtThurs.setOnClickListener {
                clickDays(position, 3)
            }
            binding.txtFri.setOnClickListener {
                clickDays(position, 4)
            }
            binding.txtSat.setOnClickListener {
                clickDays(position, 5)
            }
            binding.txtSun.setOnClickListener {
                clickDays(position, 6)
            }

            if (model.start_hour.isNotEmpty()) {
                binding.etStartTime.setText(
                    "${model.start_hour}:${model.start_min}"
                )
            } else {
                binding.etStartTime.setText("")
            }
            if (model.end_hour.isNotEmpty()) {
                binding.etEndTime.setText("${model.end_hour}:${model.end_min}")
            } else {
                binding.etEndTime.setText("")
            }

            if (model.allowed_days[0] == 1) {
                binding.txtMon.setBackgroundResource(R.drawable.iv_blue_rounded)
                binding.txtMon.setTextColor(context.resources.getColor(keyless.feature.common.R.color.white))
            } else {
                binding.txtMon.setBackgroundResource(R.drawable.iv_grey_rounded)
                binding.txtMon.setTextColor(context.resources.getColor(keyless.feature.common.R.color.black))
            }

            if (model.allowed_days[1] == 1) {
                binding.txtTues.setBackgroundResource(R.drawable.iv_blue_rounded)
                binding.txtTues.setTextColor(context.resources.getColor(keyless.feature.common.R.color.white))
            } else {
                binding.txtTues.setBackgroundResource(R.drawable.iv_grey_rounded)
                binding.txtTues.setTextColor(context.resources.getColor(keyless.feature.common.R.color.black))
            }

            if (model.allowed_days[2] == 1) {
                binding.txtWed.setBackgroundResource(R.drawable.iv_blue_rounded)
                binding.txtWed.setTextColor(context.resources.getColor(keyless.feature.common.R.color.white))
            } else {
                binding.txtWed.setBackgroundResource(R.drawable.iv_grey_rounded)
                binding.txtWed.setTextColor(context.resources.getColor(keyless.feature.common.R.color.black))
            }

            if (model.allowed_days[3] == 1) {
                binding.txtThurs.setBackgroundResource(R.drawable.iv_blue_rounded)
                binding.txtThurs.setTextColor(context.resources.getColor(keyless.feature.common.R.color.white))
            } else {
                binding.txtThurs.setBackgroundResource(R.drawable.iv_grey_rounded)
                binding.txtThurs.setTextColor(context.resources.getColor(keyless.feature.common.R.color.black))
            }

            if (model.allowed_days[4] == 1) {
                binding.txtFri.setBackgroundResource(R.drawable.iv_blue_rounded)
                binding.txtFri.setTextColor(context.resources.getColor(keyless.feature.common.R.color.white))
            } else {
                binding.txtFri.setBackgroundResource(R.drawable.iv_grey_rounded)
                binding.txtFri.setTextColor(context.resources.getColor(keyless.feature.common.R.color.black))
            }

            if (model.allowed_days[5] == 1) {
                binding.txtSat.setBackgroundResource(R.drawable.iv_blue_rounded)
                binding.txtSat.setTextColor(context.resources.getColor(keyless.feature.common.R.color.white))
            } else {
                binding.txtSat.setBackgroundResource(R.drawable.iv_grey_rounded)
                binding.txtSat.setTextColor(context.resources.getColor(keyless.feature.common.R.color.black))
            }

            if (model.allowed_days[6] == 1) {
                binding.txtSun.setBackgroundResource(R.drawable.iv_blue_rounded)
                binding.txtSun.setTextColor(context.resources.getColor(keyless.feature.common.R.color.white))
            } else {
                binding.txtSun.setBackgroundResource(R.drawable.iv_grey_rounded)
                binding.txtSun.setTextColor(context.resources.getColor(keyless.feature.common.R.color.black))
            }

            binding.etStartTime.setOnClickListener {
                context.hideKeyboard(binding.etStartTime)

                val cal = Calendar.getInstance(TimeZone.getDefault())
                cal.timeZone = TimeZone.getTimeZone(Preferences.timeZoneName.get())
                var hour: Int
                var minute: Int
                if (binding.etStartTime.text.isNotEmpty()) {
                    hour = binding.etStartTime.text.split(":")[0].toInt()
                    minute = binding.etStartTime.text.split(":")[1].toInt()
                } else {
                    hour = cal.get(Calendar.HOUR_OF_DAY)
                    minute = cal.get(Calendar.MINUTE)
                }

//                val config = context.resources.configuration
//                config.setLocale(locale)
                val timeSetListener =
                    TimePickerDialog.OnTimeSetListener { timePicker, hour, minute ->
                        cal.set(Calendar.HOUR_OF_DAY, hour)
                        cal.set(Calendar.MINUTE, minute)
//                    cal.timeZone = TimeZone.getTimeZone(Preferences.timeZoneName.get())

                        val sdfHour = SimpleDateFormat("HH", Locale("en"))
                        sdfHour.timeZone = TimeZone.getTimeZone(Preferences.timeZoneName.get())
                        val timeHour = sdfHour.format(cal.time)
                        val sdfMin = SimpleDateFormat("mm",Locale("en"))
                        sdfMin.timeZone = TimeZone.getTimeZone(Preferences.timeZoneName.get())
                        val timeMinutes = sdfMin.format(cal.time)


//                    val timeHour = SimpleDateFormat("HH").format(cal.time)
//                    val timeMinutes = SimpleDateFormat("mm").format(cal.time)
                        setStartTime(timeHour, timeMinutes, position)
                    }

                TimePickerDialog(
                    context,
                    timeSetListener,
                    hour,
                    minute,
                    false
                ).show()

            }

            binding.etEndTime.setOnClickListener {
                context.hideKeyboard(binding.etStartTime)
                val cal = Calendar.getInstance()
                cal.timeZone = TimeZone.getTimeZone(Preferences.timeZoneName.get())
                var hour: Int
                var minute: Int
                if (binding.etEndTime.text.isNotEmpty()) {
                    hour = binding.etEndTime.text.split(":")[0].toInt()
                    minute = binding.etEndTime.text.split(":")[1].toInt()
                } else {
                    hour = cal.get(Calendar.HOUR_OF_DAY)
                    minute = cal.get(Calendar.MINUTE)
                }

                val timeSetListener =
                    TimePickerDialog.OnTimeSetListener { timePicker, hour, minute ->
                        cal.set(Calendar.HOUR_OF_DAY, hour)
                        cal.set(Calendar.MINUTE, minute)
                        val sdfHour = SimpleDateFormat("HH", Locale("en"))
                        sdfHour.timeZone = TimeZone.getTimeZone(Preferences.timeZoneName.get())
                        val timeHour = sdfHour.format(cal.time)
                        val sdfMin = SimpleDateFormat("mm", Locale("en"))
                        sdfMin.timeZone = TimeZone.getTimeZone(Preferences.timeZoneName.get())
                        val timeMinutes = sdfMin.format(cal.time)
                        setEndTime(timeHour, timeMinutes, position)
                    }

                TimePickerDialog(
                    context,
                    timeSetListener,
                    hour,
                    minute,
                    false
                ).show()
            }

            binding.deleteItem.isVisible = data.filter { it.id != "0" && it.id.isNotBlank() }.size > 1 || (data[position].id == "0" && data.size != 1)

            binding.deleteItem.setOnClickListener {
//            if (modelRoutine.totalLocks.toInt() != 0){
//                if (data.size != 1){
                listener.clickOnDelete(position, model)
//                }else{
//                }
//            }else{
//                listener.clickOnDelete(position, model)
//            }
            }

        }
    }
    interface ClickOnRoutines {
        fun clickOnDelete(position: Int, model: TimeRoutineModel)
    }

}