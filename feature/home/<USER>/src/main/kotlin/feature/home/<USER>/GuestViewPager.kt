package feature.home.locks

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.fragment.app.FragmentActivity
import androidx.viewpager.widget.PagerAdapter
import keyless.feature.home.locks.R

private data class OnBoardModel(val title: String, val subTitle: String)

class GuestViewPager(context: FragmentActivity) : PagerAdapter() {

    private val layoutInflater: LayoutInflater =
        context.getSystemService(Context.LAYOUT_INFLATER_SERVICE) as LayoutInflater

    private val onGuestData = getOnGuestData()
    override fun getCount(): Int {
        return onGuestData.size
    }

    override fun isViewFromObject(view: View, o: Any): <PERSON><PERSON>an {
        return view === o as View
    }

    override fun instantiateItem(container: ViewGroup, position: Int): Any {
        val view: View = layoutInflater.inflate(R.layout.guest_pager_layout, container, false)
        // set data
        val title = onGuestData[position].title
        val subtitle = onGuestData[position].subTitle
        var txtHeading = view.findViewById<TextView>(R.id.txtHeading)
        var txtDescription = view.findViewById<TextView>(R.id.txtDescription)

        txtHeading.text = title
        txtDescription.text = subtitle

//        container.addView(view)
        container.addView(view)
        return view
    }

    override fun destroyItem(container: ViewGroup, position: Int, `object`: Any) {
        container.removeView(`object` as View)
    }

    private fun getOnGuestData(): ArrayList<OnBoardModel> {
        val context = layoutInflater.context
        return arrayListOf(
            OnBoardModel(
                context.getString(keyless.data.utils.android.R.string.welcome_to_keyless),
                context.getString(keyless.data.utils.android.R.string.one_application)
            ),
            OnBoardModel(
                context.getString(keyless.data.utils.android.R.string.share_access),
                context.getString(keyless.data.utils.android.R.string.keyless_enables)
            ),
            OnBoardModel(
                context.getString(keyless.data.utils.android.R.string.quick_services),
                context.getString(keyless.data.utils.android.R.string.choose_from_a)
            )
        )
    }
}