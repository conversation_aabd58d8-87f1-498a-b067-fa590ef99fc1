package feature.common.text

import android.text.Editable
import android.text.TextWatcher

fun trimmedTextFieldWatcher(): TextWatcher {
    return object : TextWatcher {
        override fun beforeTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {

        }

        override fun onTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {

        }

        override fun afterTextChanged(s: Editable?) {
            val trimmedText = s?.toString()?.trim()
            if (s.toString() != trimmedText) {
                s?.replace(0, s.length, trimmedText)
            }
        }
    }
}

fun noSpaceTextFieldWatcher(): TextWatcher {
    return object : TextWatcher {
        override fun beforeTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) = Unit

        override fun onTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) = Unit

        override fun afterTextChanged(s: Editable?) {
            val trimmedText = s?.toString()?.trim()?.replace(" ", "")
            if (s.toString() != trimmedText) {
                s?.replace(0, s.length, trimmedText)
            }
        }
    }
}

fun isValidPass(password: String): Boolean {
    val minLength = 8
    val hasLetter = password.any { it.isLetter() }
    val hasDigit = password.any { it.isDigit() }

    return password.length >= minLength && hasLetter && hasDigit
}
