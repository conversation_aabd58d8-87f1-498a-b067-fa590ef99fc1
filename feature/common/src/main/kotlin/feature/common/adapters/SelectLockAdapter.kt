package feature.common.adapters

import android.annotation.SuppressLint
import android.content.Context
import android.view.LayoutInflater
import android.view.ViewGroup
import android.widget.Filter
import android.widget.Filterable
import androidx.recyclerview.widget.RecyclerView
import data.common.preferences.Preferences
import data.network.android.LocksListResponse
import data.lock.common.lock.models.lock.Lock
import feature.common.dialogs.OnActionOK
import feature.common.dialogs.defaultDialog
import keyless.feature.common.databinding.LockSelectAdapterBinding

class SelectLockAdapter(
    val arrayLocks: List<AdapterLock>,
    contextListener: Context
) :
    RecyclerView.Adapter<SelectLockAdapter.ViewHolder>(), Filterable {

    lateinit var context: Context
    var listFiltered = arrayLocks
    var listener = contextListener as ClickToConnect

    override fun onCreateViewHolder(viewGroup: ViewGroup, viewType: Int): ViewHolder {
        context = viewGroup.context
        val binding = LockSelectAdapterBinding.inflate(
            LayoutInflater.from(context),
            viewGroup,
            false
        )
        return ViewHolder(binding)
    }

    @SuppressLint("SetTextI18n")
    override fun onBindViewHolder(viewHolder: ViewHolder, position: Int) {
        viewHolder.bind(listFiltered[position])
    }

    override fun getItemCount() = listFiltered.size

    inner class ViewHolder(val binding: LockSelectAdapterBinding) :
        RecyclerView.ViewHolder(binding.root) {

        fun bind(model: AdapterLock) {
            binding.txtLockName.text = listFiltered[position].name
            binding.txtLockPlace.text = listFiltered[position].place
            binding.txtPropertyName.text = listFiltered[position].property

            binding.mainLay.setOnClickListener {
                if (Preferences.isAdminLogin()) {
                    defaultDialog(
                        context,
                        context.getString(keyless.data.utils.android.R.string.disabled_in_admin_mode),
                        object : OnActionOK {
                            override fun onClickData() {
                            }
                        }
                    )
                } else {
                    listener.clickConnecting(listFiltered[position])
                }
            }
        }
    }

    override fun getFilter(): Filter {
        return object : Filter() {
            override fun performFiltering(constraint: CharSequence?): FilterResults {
                val charString = constraint?.toString() ?: ""
                listFiltered = if (charString.isEmpty()) {
                    arrayLocks
                } else {
                    arrayLocks
                        .filter {
                            (it.name.contains(constraint!!, true)) or (it.name.startsWith(constraint, true))
                        }
                        .map { it }
                }
                return FilterResults().apply { values = listFiltered }
            }

            override fun publishResults(constraint: CharSequence?, results: FilterResults?) {
                listFiltered = if (results?.values == null) {
                    ArrayList()
                } else {
                    results.values as java.util.ArrayList<AdapterLock>
                }
                notifyDataSetChanged()
            }
        }
    }

    interface ClickToConnect {
        fun clickConnecting(locksModel: AdapterLock)
    }
}

data class AdapterLock(
    val name: String,
    val place: String,
    val property: String,
    val internalId: String
)

fun LocksListResponse.LocksModel.toAdapterLock(): AdapterLock {
    return AdapterLock(
        name = lock.name,
        place = if (property_details.appartment_number.isEmpty()) {
            property_details.floor + " " + keyless.data.utils.android.R.string.txt_floor
        } else {
            property_details.appartment_number +
                    ", " + property_details.floor +
                    " " + keyless.data.utils.android.R.string.txt_floor
        },
        property = property_details.name,
        internalId = lock.internal_id
    )
}

fun Lock.toAdapter(): AdapterLock {
    return AdapterLock(
        name = name,
        place = if (property.apartmentNumber.isEmpty()) {
            property.floor + " " + keyless.data.utils.android.R.string.txt_floor
        } else {
            property.apartmentNumber +
                    ", " + property.floor +
                    " " + keyless.data.utils.android.R.string.txt_floor
        },
        internalId = internalId,
        property = property.name
    )
}