package feature.common.compose.button

import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import feature.common.compose.text.AppBodyText
import feature.common.compose.theme.AppTheme
import feature.common.compose.theme.Dimensions

@Composable
fun AppButton(
    modifier: Modifier = Modifier,
    text: String,
    enabled: Boolean,
    backgroundColor: Color = MaterialTheme.colorScheme.secondary,
    contentColor: Color = MaterialTheme.colorScheme.onSecondary,
    onClick: () -> Unit
) {
    Button(
        onClick = onClick,
        enabled = enabled,
        modifier = modifier.heightIn(min = Dimensions.Button.minHeight),
        colors = ButtonDefaults.buttonColors(
            containerColor = backgroundColor,
            contentColor = contentColor
        ),
        shape = CircleShape
    ) {
        AppBodyText(text = text, alignment = TextAlign.Center)
    }
}

@Composable
private fun AppButtonSample() {
    AppButton(text = "Hello", enabled = true, onClick = {})
}

@Preview(showBackground = true)
@Composable
private fun Preview() {
    AppTheme {
        AppButtonSample()
    }
}