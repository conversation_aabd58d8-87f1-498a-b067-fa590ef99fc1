package feature.dfu.view.dfuFirmUpdate

import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Upload
import androidx.compose.material3.LinearProgressIndicator
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import feature.dfu.data.Uploading
import no.nordicsemi.android.common.theme.view.WizardStepComponent
import no.nordicsemi.android.common.theme.view.WizardStepState

@Composable
fun DfuFirmwareUpdate() {
    WizardStepComponent(
        icon = Icons.Default.Upload,
        title = "Progress",
        state = WizardStepState.CURRENT,
        showVerticalDivider = false
    ) {
        ProgressItem(viewEntity = ProgressItemViewEntity())
    }
}

@Composable
private fun ProgressItem(viewEntity: ProgressItemViewEntity) {
    ProgressItem(
        text = BootloaderItem.toDisplayString(status = viewEntity.bootloaderStatus),
        status = viewEntity.bootloaderStatus,
        iconRightPadding = 24.dp
    )
    ProgressItem(
        text = DfuItem.toDisplayString(status = viewEntity.dfuStatus),
        status = viewEntity.dfuStatus,
        iconRightPadding = 24.dp
    )
    if (viewEntity.installationStatus == ProgressItemStatus.WORKING) {
        ProgressItem(
            text = viewEntity.progress.toLabel(),
            status = viewEntity.installationStatus,
            iconRightPadding = 24.dp
        ) {
            LinearProgressIndicator(
                progress = viewEntity.progress.progress / 100f,
                modifier = Modifier.fillMaxWidth()
            )
            Text(
                text = stringResource(
                    id = keyless.data.utils.android.R.string.dfu_display_status_progress_speed,
                    viewEntity.progress.avgSpeed
                ),
                modifier = Modifier.fillMaxWidth(),
                textAlign = TextAlign.End
            )
        }
    } else {
        ProgressItem(
            text = FirmwareItem.toDisplayString(status = viewEntity.installationStatus),
            status = viewEntity.installationStatus,
            iconRightPadding = 24.dp
        )
    }

    if (viewEntity.resultStatus != ProgressItemStatus.ERROR) {
        ProgressItem(
            text = stringResource(id = keyless.data.utils.android.R.string.dfu_progress_stage_completed),
            status = viewEntity.resultStatus,
            iconRightPadding = 24.dp
        )
    } else {
        ProgressItem(
            text = stringResource(
                id = keyless.data.utils.android.R.string.dfu_progress_stage_error,
                viewEntity.errorMessage
                    ?: stringResource(id = keyless.data.utils.android.R.string.dfu_unknown)
            ),
            status = viewEntity.resultStatus,
            iconRightPadding = 24.dp
        )
    }
}

@Composable
private fun Uploading.toLabel(): String {
    return if (partsTotal > 1) {
        stringResource(
            id = keyless.data.utils.android.R.string.dfu_display_status_progress_update_parts,
            currentPart,
            partsTotal,
            progress
        )
    } else {
        stringResource(id = keyless.data.utils.android.R.string.dfu_display_status_progress_update, progress)
    }
}

@Preview(showSystemUi = true, showBackground = true)
@Composable
fun Preview1() {
    DfuFirmwareUpdate()
}