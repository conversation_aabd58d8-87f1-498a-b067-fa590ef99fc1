package feature.dfu.data

import android.os.Parcel
import android.os.Parcelable

sealed class DfuState {
    object Idle : DfuState()
    data class InProgress(val status: DfuProgress) : DfuState()
}

sealed class DfuProgress

internal object Starting : DfuProgress()
internal object InitializingDFU : DfuProgress()

data class Uploading(
    val progress: Int = 0,
    val avgSpeed: Float = 0f,
    val currentPart: Int = 0,
    val partsTotal: Int = 0
) : DfuProgress(), Parcelable {
    constructor(parcel: Parcel) : this(
        parcel.readInt(),
        parcel.readFloat(),
        parcel.readInt(),
        parcel.readInt()
    ) {
    }

    override fun writeToParcel(parcel: Parcel, flags: Int) {
        parcel.writeInt(progress)
        parcel.writeFloat(avgSpeed)
        parcel.writeInt(currentPart)
        parcel.writeInt(partsTotal)
    }

    override fun describeContents(): Int {
        return 0
    }

    companion object CREATOR : Parcelable.Creator<Uploading> {
        override fun createFromParcel(parcel: Parcel): Uploading {
            return Uploading(parcel)
        }

        override fun newArray(size: Int): Array<Uploading?> {
            return arrayOfNulls(size)
        }
    }
}

internal object Completed : DfuProgress()
internal object Aborted : DfuProgress()
data class DfuError(val key: String, val message: String?) : DfuProgress()