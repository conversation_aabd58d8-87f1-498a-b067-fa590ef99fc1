<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    tools:context="feature.properties.PropertiesHomeFragment">

    <EditText
        android:drawablePadding="10dp"
        android:hint="@string/search_by_building_name"
        android:layout_margin="15dp"
        android:id="@+id/sv_properties"
        android:textSize="16dp"
        style="@style/mirrorText"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="30dp"
        android:background="@drawable/bg_btn_round"
        android:backgroundTint="@color/bg_edit_grey"
        android:fontFamily="@font/poppins_regular_400"
        android:singleLine="true"
        android:imeOptions="actionDone"
        android:includeFontPadding="false"
        android:ellipsize="end"
        android:drawableStart="@drawable/ic_baseline_search_24"
        android:padding="8dp"
        android:paddingStart="20dp"
        android:paddingEnd="20dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:padding="16dp"
        android:id="@+id/stopSearch"
        android:visibility="gone"
        android:src="@drawable/iv_cross_black"
        app:layout_constraintBottom_toBottomOf="@+id/sv_properties"
        app:layout_constraintEnd_toEndOf="@+id/sv_properties"
        app:layout_constraintTop_toTopOf="@+id/sv_properties" />


    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/lvLock"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginStart="10dp"
        android:layout_marginEnd="10dp"
        android:layout_marginTop="10dp"
        android:gravity="center"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/sv_properties" />


    <TextView
        android:id="@+id/noDataProperties"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:drawableTop="@drawable/iv_properties_grey"
        android:drawablePadding="15dp"
        android:fontFamily="@font/poppins_semibold_600"
        android:text="@string/no_properties"
        android:textColor="@color/black"
        android:textSize="16dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />



</androidx.constraintlayout.widget.ConstraintLayout>