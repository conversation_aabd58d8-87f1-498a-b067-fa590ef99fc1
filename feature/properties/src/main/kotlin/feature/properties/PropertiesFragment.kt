package feature.properties

import android.Manifest
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.graphics.Bitmap
import android.graphics.Canvas
import android.location.Location
import android.net.Uri
import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Filter
import android.widget.Filterable
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import androidx.core.view.isVisible
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.bumptech.glide.Glide
import com.google.android.gms.location.FusedLocationProviderClient
import com.google.android.gms.location.LocationServices
import com.google.android.gms.maps.CameraUpdateFactory
import com.google.android.gms.maps.GoogleMap
import com.google.android.gms.maps.OnMapReadyCallback
import com.google.android.gms.maps.SupportMapFragment
import com.google.android.gms.maps.model.BitmapDescriptor
import com.google.android.gms.maps.model.BitmapDescriptorFactory
import com.google.android.gms.maps.model.LatLng
import com.google.android.gms.maps.model.LatLngBounds
import com.google.android.gms.maps.model.Marker
import com.google.android.gms.maps.model.MarkerOptions
import data.common.preferences.Preferences
import data.common.preferences.Roles
import data.network.android.ApiUtils
import data.network.android.models.GetPropertyResponse
import data.network.android.models.IconModel
import data.network.android.models.Property
import data.utils.android.CommonValues
import data.utils.android.hideKeyboard
import data.utils.android.settings.SharedPreferenceUtils
import data.utils.android.toast
import keyless.feature.properties.R
import keyless.feature.properties.databinding.DashbordPropertiesBinding
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

class PropertiesFragment : Fragment(), OnMapReadyCallback, Filterable {

    private var propertiesList: java.util.ArrayList<Property> = ArrayList()
    private var viewMain: View? = null
    private var whichScreen: Int = 0
    var adapterProperties: PropertiesMarkerAdapter? = null
    lateinit var mViewModel: PropertiesViewModel
    private var mMap: GoogleMap? = null
    private lateinit var fusedLocationClient: FusedLocationProviderClient
    private lateinit var lastLocation: Location
    private lateinit var currentLatLng: LatLng
    private val sharePrefs: SharedPreferenceUtils by lazy {
        SharedPreferenceUtils.getInstance(
            requireContext()
        )
    }
    private lateinit var binding: DashbordPropertiesBinding

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = DashbordPropertiesBinding.inflate(layoutInflater)
        return binding.root
//        viewMain = inflater.inflate(R.layout.dashbord_properties, container, false)
//        return viewMain
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        if (binding != null) {
            loadMap()
            initz()
            onClicks()
            observers()
        }
    }

    private fun observers() {
        mViewModel.getResponse.observe(requireActivity()) {
            Log.e("//", "observers 1: ")
            try {
                if (it.success == true) {
                    it.properties.let {
                        binding.noInternetLayout.visibility = View.GONE
                        initAdapter(it)
                        binding.noDataProperties.visibility = View.GONE
                        addMarkersToPositions()
                    }
                } else {
                    if (whichScreen == 1) {
                        if (it.properties?.size!! <= 0) {
                            if (CommonValues.isNetworkAvailable(requireActivity())) {
                                binding.noDataProperties.isVisible = true
                                binding.noDataProperties.visibility = View.GONE
                                binding.noInternetLayout.visibility = View.GONE
                            } else {
                                binding.noInternetLayout?.visibility = View.VISIBLE
                                binding.noDataProperties.visibility = View.GONE
                            }
                        }
                    } else {
                        binding.noInternetLayout?.visibility = View.GONE
                        binding.noDataProperties.visibility = View.GONE
                    }
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }

            secondObserverWork(it)
        }

        mViewModel.error.observe(requireActivity()) {
            if (it == getString(keyless.feature.common.R.string.you_are_in_offline)) {
                if (binding.propertiesListRV.isVisible) {
                    binding.noInternetLayout?.visibility = View.VISIBLE
                    binding.svProperties?.visibility = View.GONE
                    binding.stopSearchProperties?.visibility = View.GONE
                    adapterProperties?.updateAdapter(null)
                }
            } else {
                requireActivity().toast(it)
            }
        }
    }

    private fun secondObserverWork(it: GetPropertyResponse) {
        Log.e("//", "observers 2: ")

        val markerOptionsList = mutableListOf<MarkerOptions>()
        it.properties?.forEach { it1 ->
            if (!it1.latitude.equals("null")) {
                if (it1.latitude?.contains(",") == true) {
                    val lat = it1.latitude?.replace(",", ".")
                    val long = it1.longitude?.replace(",", ".")
                    lat?.toDoubleOrNull()?.let { lat ->
                        val latLng = LatLng(lat, long!!.toDouble())
                        if (it1.latitude!!.contains(".")) {
                            markerOptionsList.add(
                                MarkerOptions().position(
                                    LatLng(
                                        it1.latitude!!.toDouble(),
                                        it1.longitude!!.toDouble()
                                    )
                                )
                            )
                        }

                        for (markerOptions in markerOptionsList) {
                            val marker = mMap?.addMarker(
                                addMarkerOption(
                                    latLng,
                                    it1.icon
                                )
                            )
                            marker?.tag = it1._id
                        }
                    }
                } else {
                    it1.latitude?.toDoubleOrNull()?.let { lat ->
                        val latLng =
                            LatLng(it1.latitude!!.toDouble(), it1.longitude!!.toDouble())
                        if (it1.latitude!!.contains(".")) {
                            markerOptionsList.add(
                                MarkerOptions().position(
                                    LatLng(
                                        it1.latitude!!.toDouble(),
                                        it1.longitude!!.toDouble()
                                    )
                                )
                            )
                        }

                        for (markerOptions in markerOptionsList) {
                            val marker = mMap?.addMarker(
                                addMarkerOption(
                                    latLng,
                                    it1.icon
                                )
                            )
                            marker?.tag = it1._id
                        }
                    }
                }
            }
        }

        if (it.properties?.size!! > 0) {
            binding.svProperties.isVisible = whichScreen == 1
            if (it.properties!![0].latitude?.contains(",") == true) {
//                        val lat = it.properties!![0].latitude?.replace(",", ".")
//                        val long = it.properties!![0].longitude?.replace(",", ".")
//                        val latLng = LatLng(
//                            lat!!.toDouble(),
//                            long!!.toDouble()
//                        )
                val builder = LatLngBounds.Builder()

                for (markerOptions in markerOptionsList) {
                    builder.include(markerOptions.position)
                }
                val bounds = builder.build()

                if (areBoundsTooSmall(bounds, 300)) {
                    mMap?.animateCamera(CameraUpdateFactory.newLatLngZoom(bounds.center, 17f))
                } else {
                    val width = resources.displayMetrics.widthPixels
                    val height = resources.displayMetrics.heightPixels
                    val padding = (width * 0.10).toInt() // offset from edges of the map 10% of screen
                    val cu = CameraUpdateFactory.newLatLngBounds(bounds, width, height, 20)

                    mMap!!.animateCamera(cu)
                }
            } else {
//                        val latLng = LatLng(
//                            it.properties!![0].latitude!!.toDouble(),
//                            it.properties!![0].longitude!!.toDouble()
//                        )
                try {
                    val builder = LatLngBounds.Builder()
                    for (markerOptions in markerOptionsList) {
                        builder.include(markerOptions.position)
                    }
                    val bounds = builder.build()

                    if (areBoundsTooSmall(bounds, 300)) {
                        mMap?.animateCamera(
                            CameraUpdateFactory.newLatLngZoom(
                                bounds.center,
                                17f
                            )
                        )
                    } else {
                        val width = resources.displayMetrics.widthPixels
                        val height = resources.displayMetrics.heightPixels
                        val padding =
                            (width * 0.10).toInt() // offset from edges of the map 10% of screen
                        val cu =
                            CameraUpdateFactory.newLatLngBounds(bounds, width, height, 20)

                        mMap!!.animateCamera(cu)
                    }
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            }
        } else {
            binding.noDataProperties.isVisible = whichScreen == 1
            val latLng = LatLng(
                25.2048,
                55.2708
            )

            val markerOptions = MarkerOptions()
            markerOptions.position(latLng)
            mMap?.addMarker(markerOptions)
            mMap?.moveCamera(CameraUpdateFactory.newLatLngZoom(LatLng(25.2048, 55.2708), 6f))
        }

        mMap?.setPadding(50, 50, 50, 50)
    }

    private fun bitmapDescriptorFromVector(context: Context, vectorResId: Int): BitmapDescriptor {
        val vectorDrawable = ContextCompat.getDrawable(context, vectorResId)
        vectorDrawable!!.setBounds(
            0,
            0,
            vectorDrawable.intrinsicWidth,
            vectorDrawable.intrinsicHeight
        )
        val bitmap = Bitmap.createBitmap(
            vectorDrawable.intrinsicWidth,
            vectorDrawable.intrinsicHeight,
            Bitmap.Config.ARGB_8888
        )
        val canvas = Canvas(bitmap)
        vectorDrawable.draw(canvas)
        return BitmapDescriptorFactory.fromBitmap(bitmap)
    }

    private fun addMarkerOption(
        latLng: LatLng,
        iconList: ArrayList<IconModel>?
    ): MarkerOptions {
        val markerOptions = MarkerOptions().position(latLng)
        try {

            var icon = keyless.data.utils.android.R.drawable.marker_other
            if (iconList?.isNotEmpty() == true) {
                val iconModel = iconList.first()
                when (iconModel.name) {
                    activity?.getString(keyless.data.utils.android.R.string.text_villa) -> {
                        icon = keyless.data.utils.android.R.drawable.marker_villa
                    }

                    activity?.getString(keyless.data.utils.android.R.string.text_hotel) -> {
                        icon = keyless.data.utils.android.R.drawable.marker_hotel
                    }

                    activity?.getString(keyless.data.utils.android.R.string.text_office) -> {
                        icon = keyless.data.utils.android.R.drawable.marker_office
                    }

                    activity?.getString(keyless.data.utils.android.R.string.text_apartment) -> {
                        icon = keyless.data.utils.android.R.drawable.marker_apartment
                    }
                }
            }

            markerOptions.icon(bitmapDescriptorFromVector(requireActivity(), icon))

        }catch (e: Exception){
            e.printStackTrace()
        }
        return markerOptions
    }

    private fun addMarkersToPositions() {
        mViewModel.getResponse.value?.properties?.let {
            mMap?.clear()
        }
//        activity?.let {
//            mViewModel.getResponse.observe(requireActivity()) {
//
//            }
//        }
    }

    private fun areBoundsTooSmall(bounds: LatLngBounds, minDistanceInMeter: Int): Boolean {
        val result = FloatArray(1)
        Location.distanceBetween(
            bounds.southwest.latitude,
            bounds.southwest.longitude,
            bounds.northeast.latitude,
            bounds.northeast.longitude,
            result
        )
        return result[0] < minDistanceInMeter
    }

    private fun initz() {
        mViewModel = ViewModelProvider(this)[PropertiesViewModel::class.java]
        fusedLocationClient = LocationServices.getFusedLocationProviderClient(requireActivity())
        binding.addProperty.isVisible =
            !(Preferences.role.get() == Roles.CUSTOMER_SERVICES || Preferences.role.get() == Roles.VIEWER_ACCESS)
    }

    override fun onResume() {
        super.onResume()
        binding.svProperties.setText("")
        binding.stopSearchProperties.isVisible = false
        mViewModel.hitAllProperty(SharedPreferenceUtils.getInstance(requireActivity()).token)
    }

    override fun onMapReady(p0: GoogleMap) {
        activity?.let { currentActivity ->
            binding.mapLayout?.isVisible = true
            mMap = p0
            if (
                ActivityCompat.checkSelfPermission(
                    currentActivity,
                    Manifest.permission.ACCESS_FINE_LOCATION
                ) != PackageManager.PERMISSION_GRANTED &&
                ActivityCompat.checkSelfPermission(
                    requireActivity(),
                    Manifest.permission.ACCESS_COARSE_LOCATION
                ) != PackageManager.PERMISSION_GRANTED
            ) {
            } else {
                fusedLocationClient.lastLocation.addOnSuccessListener(requireActivity()) { location ->
                    if (location != null) {
                        lastLocation = location
                        currentLatLng = LatLng(location.latitude, location.longitude)
//                        mMap?.moveCamera(CameraUpdateFactory.newLatLngZoom(currentLatLng, 14f))
//                    mMap.animateCamera(CameraUpdateFactory.newLatLngZoom(currentLatLng, 14f))
                    }
                }
                adapterProperties?.let {
                    mMap?.clear()
                    addMarkersToPositions()
                }
            }

            mMap?.isMyLocationEnabled = false
            if (mMap?.isMyLocationEnabled == false) {
                mMap?.moveCamera(CameraUpdateFactory.newLatLngZoom(LatLng(25.2048, 55.2708), 6f))
            }
            onMapClick()
        }
    }

    private fun onMapClick() {
        mMap?.setOnMarkerClickListener { marker ->
            val property =
                mViewModel.getResponse.value?.properties?.filter { true == it._id?.equals(marker.tag) }
            if (property?.isNotEmpty() == true) {
                val first = property.first()
                setDataPropertyItem(first, marker)
            }
            false
        }

        mMap?.setOnMapClickListener {
            binding.propertyItemView.isVisible = false
        }

//        mMap?.setOnCameraMoveStartedListener {
//            binding.propertyItemView.isVisible = false
//        }
    }

    private fun loadMap() {
        try {
            lifecycleScope.launch {
                delay(200)
                val fm = childFragmentManager
                val supportMapFragment = SupportMapFragment.newInstance()
                fm.beginTransaction().replace(binding.mapFragment.id, supportMapFragment).commit()
                supportMapFragment.getMapAsync(this@PropertiesFragment)
            }
        } catch (e: java.lang.Exception) {
            e.printStackTrace()
        }
    }

    private fun onClicks() {
        binding.svProperties.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {
            }

            override fun onTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {
//                adapterProperties?.filter?.filter(p0.toString())
//                lifecycleScope.launch{
//                    delay(100)
//                    if (adapterProperties?.listFiltered?.isEmpty() == true){
//                        binding.noDataProperties.isVisible = whichScreen == 1
//                    }
//                }
//
//                binding.stopSearchProperties.isVisible = p0.toString().isNotEmpty()

                adapterProperties?.let {
                    filter.filter(p0.toString())
                    binding.stopSearchProperties.isVisible = p0.toString().isNotEmpty()
                }
            }

            override fun afterTextChanged(p0: Editable?) {
            }
        })

        binding.stopSearchProperties.setOnClickListener {
            hideKeyboard()
            binding.svProperties.setText("")
            filter?.filter("")
        }

        binding.addProperty.setOnClickListener {
            if (CommonValues.isNetworkAvailable(requireContext())) {
                startActivity(Intent(requireActivity(), feature.properties.AddProperty::class.java))
            } else {
                requireActivity().toast(getString(keyless.feature.common.R.string.you_are_in_offline))
            }
        }

        binding.mapRadio.setOnClickListener {
            hideKeyboard()
            whichScreen = 0
            binding.svProperties.setText("")
            binding.svProperties.isVisible = false
            binding.stopSearchProperties.isVisible = false
            binding.noDataProperties.isVisible = false
            binding.noInternetLayout?.isVisible = false
            binding.mapLayout.isVisible = true
            binding.ivLock.setImageResource(keyless.feature.common.R.drawable.iv_checked_radio)
            binding.ivProperties.setImageResource(
                keyless.feature.common.R.drawable.iv_not_checked_grey
            )
            binding.propertiesListRV.isVisible = false
            binding.svProperties.isVisible = false
        }
        binding.listRadio.setOnClickListener {
            hideKeyboard()
            binding.svProperties.setText("")
            binding.svProperties.isVisible = true
            binding.stopSearchProperties.isVisible = false
            whichScreen = 1
            binding.mapLayout.isVisible = false
            binding.propertyItemView.isVisible = false
            binding.ivLock.setImageResource(
                keyless.feature.common.R.drawable.iv_not_checked_grey
            )
            binding.ivProperties.setImageResource(
                keyless.feature.common.R.drawable.iv_checked_radio
            )
            binding.propertiesListRV.isVisible = true
            mViewModel.hitAllProperty(SharedPreferenceUtils.getInstance(requireActivity()).token)
        }
//        titlePage.setOnClickListener {
//            binding.propertyItemView.isVisible = true
//        }
    }

    override fun getFilter(): Filter {
        var listFiltered = propertiesList
        return object : Filter() {
            override fun performFiltering(constraint: CharSequence?): FilterResults {
                val charString = constraint?.toString() ?: ""
                listFiltered = if (charString.isEmpty()) {
                    propertiesList
                } else {
                    val mFilteredList = ArrayList<Property>()
                    propertiesList.filter {
                        (it.building_name!!.contains(constraint!!, true)) or
                            (it.building_name!!.startsWith(constraint, true))
                    }
                        .forEach { mFilteredList.add(it) }
                    mFilteredList
                }
                return FilterResults().apply { values = listFiltered }
            }

            override fun publishResults(constraint: CharSequence?, results: FilterResults?) {
                listFiltered = if (results?.values == null) {
                    ArrayList()
                } else {
                    results.values as java.util.ArrayList<Property>
                }
                if (binding.noDataProperties != null) {
                    if (whichScreen == 1) {
                        binding.noDataProperties.isVisible = listFiltered.size == 0
                    } else {
                        binding.noDataProperties.isVisible = false
                    }
                }
                adapterProperties?.updateAdapter(listFiltered)
            }
        }
    }

    private fun setDataPropertyItem(property: Property?, marker: Marker) {
        binding.propertyItemView.isVisible = true
        binding.includeMarkLayout.placeName.text = property?.building_name
        binding.includeMarkLayout.addressMarker.text = property?.area + ", " + property?.emirate
        binding.includeMarkLayout.noFloorTxt.text = property?.total_floors.toString()
        binding.includeMarkLayout.noLockTxt.text = property?.total_locks.toString()
        val iconNotEmpty = property?.icon?.isNotEmpty()
        if (iconNotEmpty == true) {
            val iconModel = property.icon?.first()
            Glide.with(requireActivity()).load(ApiUtils.BASE_URL + "/" + iconModel?.icon)
                .into(binding.includeMarkLayout.iconProperty)
        }
        binding.viewDetailBtn.setOnClickListener {
            binding.propertyItemView.isVisible = false
            startActivity(
                Intent(
                    requireActivity(),
                    feature.properties.PropertyDetailActivity::class.java
                ).putExtra("model", property)
            )
        }
        binding.directionBtn.setOnClickListener {
            val sa =
                "google.navigation:q=\" + ${marker.position.latitude} + \",\" " +
                    "+ ${marker.position.longitude} + \"&mode=d"
            val s = "google.navigation:q=${marker.position.latitude},${marker.position.longitude}"
            val i = Intent(Intent.ACTION_VIEW, Uri.parse(s))
            i.setClassName(
                "com.google.android.apps.maps",
                "com.google.android.maps.MapsActivity"
            )
//            i.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            startActivity(i)
        }
    }

    private fun initAdapter(properties: ArrayList<Property>) {
        activity?.let {
            adapterProperties = PropertiesMarkerAdapter()
            binding.propertiesListRV.layoutManager = LinearLayoutManager(it)
            binding.propertiesListRV.adapter = adapterProperties
            adapterProperties?.updateAdapter(properties)
            propertiesList = properties
        }
    }
}