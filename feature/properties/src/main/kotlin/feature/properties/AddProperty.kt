package feature.properties

import android.app.Dialog
import android.content.Intent
import android.graphics.Color
import android.graphics.Rect
import android.graphics.drawable.ColorDrawable
import android.os.Build
import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.util.Log
import android.view.Gravity
import android.view.View
import android.view.WindowManager
import android.widget.EditText
import android.widget.PopupMenu
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import data.network.android.ApiUtils
import data.network.android.models.IconModel
import data.network.android.models.ModelAdminInstaller
import data.network.android.models.Property
import data.network.android.models.SearchResultModel
import data.utils.android.CommonValues
import feature.common.dialogs.ProgressDialogUtils
import data.utils.android.settings.SharedPreferenceUtils
import data.utils.android.toast
import feature.common.icons.SelectIconAdapter
import keyless.feature.properties.R
import keyless.feature.properties.databinding.ActivityAddPropertyBinding

class AddProperty : AppCompatActivity(), SelectIconAdapter.SetIcon {
    private var iconId: String = ""
    private lateinit var mViewModel: AddProViewModel
    private lateinit var iconAdapterActivity: SelectIconAdapter
    private var model: Property? = null
    private var searchResult: SearchResultModel? = null
    private var lat: String? = null
    private var lng: String? = null
    lateinit var emirateList: ArrayList<String>
    private lateinit var binding: ActivityAddPropertyBinding

    private var searchActivityCallBack =
        registerForActivityResult(ActivityResultContracts.StartActivityForResult()) {
            if (it.resultCode == RESULT_OK) {
                searchResult = it.data?.getParcelableExtra("result")

                lat = if (searchResult?.latlng?.latitude.toString().contains(",")) {
                    searchResult?.latlng?.latitude.toString().replace(",", ".")
                } else {
                    searchResult?.latlng?.latitude.toString()
                }
                lng = if (searchResult?.latlng?.longitude.toString().contains(",")) {
                    searchResult?.latlng?.longitude.toString().replace(",", ".")
                } else {
                    searchResult?.latlng?.longitude.toString()
                }
                binding.selectLocET.setText(searchResult?.title?.trim())
            }
        }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityAddPropertyBinding.inflate(layoutInflater)
        setContentView(binding.root)
        init()
        onClicks()
        getDataIntent()
        observeApi()
    }

    private fun observeApi() {
        mViewModel.getResponse.observe(this) {
            it.message?.let {
                toast(it)
            }
            CommonValues.refreshApi = true
            ProgressDialogUtils.getInstance().hideProgress()
            if (it.success) {
                finish()
            }
        }

        mViewModel.getResponseInstallerList.observe(this) {
            ProgressDialogUtils.getInstance().hideProgress()
            setResult(20)
            finish()
        }

        mViewModel.error.observe(this) {
            runOnUiThread {
                Log.e("//", "observeApi: ")
                ProgressDialogUtils.getInstance().hideProgress()
                toast(it)
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        ProgressDialogUtils.getInstance().hideProgress()
        ProgressDialogUtils.getInstance().dispose()
    }

    private fun init() {
        mViewModel = ViewModelProvider(this)[AddProViewModel::class.java]
        emirateList = arrayListOf(
            getString(keyless.feature.common.R.string.text_abu_dhabi),
            getString(keyless.feature.common.R.string.text_dubai),
            getString(keyless.feature.common.R.string.text_sharjah),
            getString(keyless.feature.common.R.string.text_ajman),
            getString(keyless.feature.common.R.string.text_umm_al_quwain),
            getString(keyless.feature.common.R.string.text_ras_al_khaimah),
            getString(keyless.feature.common.R.string.text_fujairah)
        )
    }

    private fun setData() {
        model?.let {
            binding.propertyTitle.text = getString(keyless.data.utils.android.R.string.text_edit_property)
            if (true == it.icon?.isNotEmpty()) {
                it.icon?.first()?.let {
                    iconId = it._id
                    setImage(it.icon)
                }
            }
            binding.selectLocET.setText(it.area)
            binding.emirateET.setText(it.emirate)
            binding.buildingET.setText(it.building_name)
            binding.floorET.setText(it.total_floors.toString())
            lat = it.latitude.toString()
            lng = it.longitude.toString()
            try {
                binding.maintenanceET.setText(it.grocery_number)
                binding.laundryET.setText(it.laundary_number)
            } catch (e: Exception) {
                binding.maintenanceET.setText("")
                binding.laundryET.setText("")
            }

            binding.supportCallET.setText(it.support_call_number)
            binding.supportWhatsappET.setText(it.support_whatsapp_number)
            binding.saveBtn.text = getString(keyless.feature.common.R.string.update)
            binding.textView10.text = getString(keyless.data.utils.android.R.string.change_icon)
        }
    }

    private fun getDataIntent() {
        if (Build.VERSION.SDK_INT >= 33) {
            model = intent.getParcelableExtra("model", Property::class.java)
        } else {
            model = intent?.getParcelableExtra("model")
        }

        setData()
    }

    private fun onClicks() {
        binding.iconLayout.setOnClickListener {
            iconAdapterActivity = SelectIconAdapter(this)
            ProgressDialogUtils.getInstance().showProgress(this, true)
            mViewModel.getIcons(this) { iconModel ->
                ProgressDialogUtils.getInstance().hideProgress()
                iconModel?.let {
                    val iconModels = ArrayList<IconModel>()
                    for (i in it.icons) {
                        if (i.type == "property") {
                            iconModels.add(i)
                        }
                    }
                    selectIconDialog(iconModels)
                    binding.textView10.text = getString(keyless.data.utils.android.R.string.change_icon)
                }
            }
        }

        binding.selectLocET.setOnClickListener {
            searchActivityCallBack.launch(
                Intent(this, SearchLocationActivity::class.java)
                    .putExtra("lat", lat)
                    .putExtra("lng", lng)
            )
        }
        binding.backBtnProperty.setOnClickListener {
            if (intent.hasExtra("installer")) {
                setResult(90)
            }
            finish()
        }
        binding.saveBtn.setOnClickListener {
            if (intent.hasExtra("installer")) {
                var modelInstaller =
                    intent.getParcelableExtra<ModelAdminInstaller.DataModelInstaller>("installer")

                saveInstallerProperty(modelInstaller!!.company[0]._id)
            } else {
                saveDataApi()
            }
        }

        binding.emirateET.setOnClickListener {
            showEmirateSpinner(it)
        }
    }

    private fun saveInstallerProperty(id: String) {
        if (isValid()) {
            ProgressDialogUtils.getInstance().showProgress(this, true)
            val supportCall = binding.supportCallET.text.toString()
            val supportWhatsapp = binding.supportWhatsappET.text.toString()
            mViewModel.hitCreateInstallerProperty(
                area = binding.selectLocET.text.toString(),
                total_floors = binding.floorET.text.toString(),
                building_name = binding.buildingET.text.toString(),
                emirate = binding.emirateET.text.toString(),
                longitude = lng.toString(),
                token = SharedPreferenceUtils.getInstance(this).token,
                latitude = lat.toString(),
                grocery_number = binding.maintenanceET.text.toString(),
                laundary_number = binding.laundryET.text.toString(),
                icon_id = iconId,
                supportCall = supportCall,
                whatsapp = supportWhatsapp, id
            )
        }
    }

    private fun selectIconDialog(
        arrayIcons: java.util.ArrayList<IconModel>
    ) {
        val dialog = Dialog(this)
        dialog.window?.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
        dialog.setContentView(keyless.feature.common.R.layout.select_icon_dialog)
        dialog.window?.decorView?.addOnLayoutChangeListener { v, _, _, _, _, _, _, _, _ ->
            val displayRectangle = Rect()
            val window = dialog.window
            v.getWindowVisibleDisplayFrame(displayRectangle)
            val maxHeight = displayRectangle.height() * 0.9f // 60%
            val maxWidth = displayRectangle.width() * 0.9f // 60%
            window?.setLayout(maxWidth.toInt(), maxHeight.toInt())
        }

        var rvIcon = dialog.findViewById<RecyclerView>(keyless.feature.common.R.id.rv_icon)
        var svLock = dialog.findViewById<EditText>(keyless.feature.common.R.id.sv_lock)

        dialog.setCancelable(true)
        dialog.setCanceledOnTouchOutside(true)
        dialog.window!!.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_STATE_HIDDEN)
        dialog.show()
        rvIcon.layoutManager = GridLayoutManager(this, 3)
        rvIcon.adapter = iconAdapterActivity

        iconAdapterActivity.updateValues(arrayIcons, dialog)

        svLock.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {
            }

            override fun onTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {
                iconAdapterActivity.filter.filter(p0.toString())
            }

            override fun afterTextChanged(p0: Editable?) {
            }
        })
    }

    private fun showEmirateSpinner(view: View) {
        val popup = PopupMenu(this, view, keyless.feature.common.R.style.PopupMenu)
        popup.gravity = Gravity.START
        for (i in 0 until emirateList.size) {
            popup.menu.add(emirateList[i])
        }
        popup.setOnMenuItemClickListener { item ->
            binding.emirateET.setText(item.title)
            true
        }
        popup.show()
    }

    private fun isEdit() = model != null

    private fun saveDataApi() {
        if (isValid()) {
            ProgressDialogUtils.getInstance().showProgress(this, true)
            val supportCall = binding.supportCallET.text.toString()
            val supportWhatsapp = binding.supportWhatsappET.text.toString()
            if (isEdit()) {
                mViewModel.updateProperty(
                    propertyID = model?._id!!,
                    area = binding.selectLocET.text.toString(),
                    total_floors = binding.floorET.text.toString(),
                    building_name = binding.buildingET.text.toString(),
                    emirate = binding.emirateET.text.toString(),
                    longitude = lng.toString(),
                    token = SharedPreferenceUtils.getInstance(this).token,
                    latitude = lat.toString(),
                    grocery_number = binding.maintenanceET.text.toString(),
                    laundary_number = binding.laundryET.text.toString(),
                    icon_id = iconId,
                    callString = supportCall,
                    whatsapp = supportWhatsapp, context = this
                ) {
                    CommonValues.refreshApi = true
                    if (it?.success!!) {
                        updateModel()
                        setResult(RESULT_OK, Intent().putExtra("model", model))
                        finish()
                    } else {
                        runOnUiThread {
                            ProgressDialogUtils.getInstance().hideProgress()
                            toast(it.message!!)
                        }
                    }
                }
            } else {
                mViewModel.hitGetCreatePropertyApi(
                    area = binding.selectLocET.text.toString(),
                    total_floors = binding.floorET.text.toString(),
                    building_name = binding.buildingET.text.toString(),
                    emirate = binding.emirateET.text.toString(),
                    longitude = lng.toString(),
                    token = SharedPreferenceUtils.getInstance(this).token,
                    latitude = lat.toString(),
                    grocery_number = binding.maintenanceET.text.toString(),
                    laundary_number = binding.laundryET.text.toString(),
                    icon_id = iconId,
                    supportCall = supportCall,
                    whatsapp = supportWhatsapp
                )
            }
        }
    }

    private fun updateModel() {
        model?.area = binding.selectLocET.text.toString()
        model?.total_floors = binding.floorET.text.toString()
        model?.building_name = binding.buildingET.text.toString()
        model?.emirate = binding.emirateET.text.toString()
        model?.longitude = lng
        model?.latitude = lat
        model?.grocery_number = binding.maintenanceET.text.toString()
        model?.laundary_number = binding.laundryET.text.toString()
        model?.icon_id = iconId
        model?.support_call_number = binding.supportCallET.text.toString()
        model?.support_whatsapp_number = binding.supportWhatsappET.text.toString()
    }

    private fun isValid(): Boolean =
        if (binding.selectLocET.text.isEmpty()) {
            toast(getString(keyless.data.utils.android.R.string.please_select_address))
            false
        } else if (binding.emirateET.text.toString().isEmpty()) {
            toast(getString(keyless.data.utils.android.R.string.please_select_emirate))
            false
        } else if (binding.buildingET.text.toString().isEmpty()) {
            toast(getString(keyless.data.utils.android.R.string.please_enter_building_name))
            false
        } else if (binding.floorET.text.toString().isEmpty()) {
            toast(getString(keyless.data.utils.android.R.string.please_enter_total_floor))
            false
        } else if (iconId.isEmpty()) {
            toast(getString(keyless.data.utils.android.R.string.please_select_icon))
            false
        } else {
            true
        }

    override fun getIcon(iconModelAll: IconModel) {
//        iv_icon.setImageResource(0)
        model?.icon?.clear()
        model?.icon?.add(iconModelAll)
        iconId = iconModelAll._id
        setImage(iconModelAll.icon)
    }

    fun setImage(id: String) {
        Glide.with(this)
            .load(ApiUtils.IMAGE_BASE_URL + id)
            .into(binding.placeHolder)
    }
}