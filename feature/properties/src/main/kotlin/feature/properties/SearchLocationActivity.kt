package feature.properties

import android.app.Activity
import android.content.Intent
import android.location.Address
import android.location.Geocoder
import android.location.Location
import android.os.Bundle
import android.view.View
import android.view.inputmethod.InputMethodManager
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import com.google.android.gms.maps.CameraUpdateFactory
import com.google.android.gms.maps.GoogleMap
import com.google.android.gms.maps.OnMapReadyCallback
import com.google.android.gms.maps.SupportMapFragment
import com.google.android.gms.maps.model.LatLng
import com.google.gson.GsonBuilder
import data.network.android.ApiServices
import data.network.android.models.SearchResultModel
import keyless.feature.properties.databinding.ActivitySearchLocationBinding
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory
import java.util.*

class SearchLocationActivity : AppCompatActivity(), OnMapReadyCallback, LocationChangedListener {
    var map: GoogleMap? = null
    var api: ApiServices? = null
    var adapter: PlaceAPIAdapter? = null
    var gpsTracker: GpsTracker? = null
    private lateinit var binding: ActivitySearchLocationBinding

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivitySearchLocationBinding.inflate(layoutInflater)
        setContentView(binding.root)
        api = initApiServices()
        loadMap()
        onClicks()
        search()
        initSearchAdapter()
        gpsTracker = GpsTracker(this)
        gpsTracker?.requestPermission()
    }

    private fun initSearchAdapter() {
        adapter = PlaceAPIAdapter(this, android.R.layout.simple_list_item_1, this)
        binding.searchLocET.setAdapter(adapter)

//        searchLocET.addTextChangedListener(object : TextWatcher {
//            override fun beforeTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {
//
//            }
//
//            override fun onTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {
//                adapter = PlaceAPIAdapter(this@SearchLocationActivity, android.R.layout.simple_list_item_1, this@SearchLocationActivity)
//                searchLocET.setAdapter(adapter)
//            }
//
//            override fun afterTextChanged(p0: Editable?) {
//
//            }
//        })
    }

    private fun search() {
        binding.searchLocET.setOnItemClickListener { adapterView, view, i, l ->
            try {
                hideKeyboard(this)
                val item = adapter?.resultList
                item?.get(i)?.let {
                    lifecycleScope.launch(Dispatchers.IO) {
                        adapter?.getLocationFormAddress(this@SearchLocationActivity, it)?.let {
                            withContext(Dispatchers.Main) {
                                addMarker(it)
                            }
                        }
                    }
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }

//        searchLocET.setOnEditorActionListener { _, actionId, _ ->
//            if (actionId == EditorInfo.IME_ACTION_SEARCH) {
//                performSearch()
//            }
//            true
//        }
    }

    private fun performSearch() {
        hideKeyboard(this)
//        item?.get(i)?.let {
        try {
            lifecycleScope.launch(Dispatchers.IO) {
                adapter?.getLocationFormAddress(
                    this@SearchLocationActivity,
                    binding.searchLocET.text.toString()
                )?.let {
                    withContext(Dispatchers.Main) {
                        addMarker(it)
                    }
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
//        }
    }

    private fun addMarker(it: LatLng) {
        map?.animateCamera(CameraUpdateFactory.newLatLngZoom(it, 18f), 300, null)
    }

    private fun initApiServices(): ApiServices? {
        val retrofit = Retrofit.Builder()
            .addConverterFactory(GsonConverterFactory.create(GsonBuilder().setLenient().create()))
            .baseUrl("https://maps.googleapis.com/maps/api/place/")
            .build()
        return retrofit.create(ApiServices::class.java)
    }

    private fun onClicks() {
        binding.backBtnProperty.setOnClickListener { finish() }
    }

    private fun mapClicks() {
        map?.setOnCameraIdleListener { fetchAddress(map?.cameraPosition?.target) }
//        map.setOnMapClickListener {
//            fetchAddress(it)
//        }
//        map.setOnMarkerClickListener {
//            fetchAddress(it.position)
//            false
//        }
    }

    private fun fetchAddress(latLng: LatLng?) {
//        val sheetDialog = BottomSheetDialog(this)
//        sheetDialog.setContentView(R.layout.searchlocation_popup)
//        sheetDialog.show()
        var mTitle: String = ""
        lifecycleScope.launch(Dispatchers.IO) {
            getCompleteAddressString(latLng) { title, address ->
                mTitle = title
                withContext(Dispatchers.Main) {
                    binding.locAddress.text = address
                }
            }
        }
        binding.saveBtnAdd.setOnClickListener {
            if (mTitle.isNotEmpty() && latLng != null) {
                var searchModel = SearchResultModel()
                searchModel.latlng = latLng
                searchModel.title = mTitle
                setResult(RESULT_OK, Intent().putExtra("result", searchModel))
                finish()
            }
        }
    }

    private fun loadMap() {
        val fm = supportFragmentManager
        val supportMapFragment = SupportMapFragment.newInstance()
        val map =
            fm.beginTransaction().replace(binding.searchMapContainer.id, supportMapFragment).commit()
        supportMapFragment.getMapAsync(this)
    }

    override fun onMapReady(p0: GoogleMap) {
        map = p0
        map?.moveCamera(CameraUpdateFactory.newLatLngZoom(LatLng(25.2048, 55.2708), 12f))
        mapClicks()
    }

    private suspend fun getCompleteAddressString(latlng: LatLng?, callback: suspend (String, String) -> Unit) {
        var strAdd = getString(keyless.data.utils.android.R.string.no_address_found)
        val geocoder = Geocoder(this, Locale("en"))
        try {
            val addresses: List<Address>? = geocoder.getFromLocation(
                latlng?.latitude ?: 0.0,
                latlng?.longitude ?: 0.0,
                1
            )
            if (addresses != null) {
                val returnedAddress: Address = addresses[0]
                val strReturnedAddress = StringBuilder("")
                for (i in 0..returnedAddress.maxAddressLineIndex) {
                    strReturnedAddress.append(returnedAddress.getAddressLine(i)).append("\n")
                }
                strAdd = strReturnedAddress.toString()
                var title = ""
                try {
                    val address: String = addresses[0].getAddressLine(0)
                    val city: String = addresses[0].locality
                    val knownName: String? = addresses[0].featureName
//                    title = if (knownName.isNullOrEmpty()){
//                        city
//                    }else knownName
//                    title = city+", "+address
                } catch (e: Exception) {
                    title = strAdd
                }
                title = strAdd
                callback(title, strAdd)
                return
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
        callback("", strAdd)
    }

    override fun locationChanged(location: Location?) {
        lifecycleScope.launch {
            location?.let {
                val latLng = LatLng(it.latitude, it.longitude)
                addMarker(latLng)
                delay(2000)
                fetchAddress(latLng)
            }
        }
    }
    private fun hideKeyboard(activity: Activity) {
        val imm: InputMethodManager =
            activity.getSystemService(INPUT_METHOD_SERVICE) as InputMethodManager
        // Find the currently focused view, so we can grab the correct window token from it.
        var view: View? = activity.currentFocus
        // If no view currently has focus, create a new one, just so we can grab a window token from it
        if (view == null) {
            view = View(activity)
        }
        imm.hideSoftInputFromWindow(view.windowToken, 0)
    }
}