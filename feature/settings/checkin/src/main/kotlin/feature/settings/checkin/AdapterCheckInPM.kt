package feature.settings.checkin

import android.annotation.SuppressLint
import android.content.Context
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import data.user.guest.models.GuestAssignment
import feature.common.device.Datetime
import keyless.feature.settings.checkin.R
import keyless.feature.settings.checkin.databinding.AdapterCheckInPmLayBinding

class AdapterCheckInPM(contextActivity: Context) :
    RecyclerView.Adapter<AdapterCheckInPM.ViewHolder>() {

    lateinit var context: Context
    private var listener = contextActivity as ClickCheckIn
    private var arrayIconsNew = ArrayList<ModelAssignCheckIns>()
    private var listFiltered = ArrayList<ModelAssignCheckIns>()

    inner class ViewHolder(val binding: AdapterCheckInPmLayBinding) :
        RecyclerView.ViewHolder(binding.root) {

        internal fun bind(model: ModelAssignCheckIns, position: Int) {
            binding.guestName.text = context.getString(R.string.guest_name) +
                    " " + model.firstName + " " + model.lastName
            binding.lockName.text = context.getString(R.string.lock_name_) +
                    " " + model.lockName
            binding.bookingNumber.text = context.getString(R.string.booking_number) +
                    " " + model.bookingNumber
            binding.bookingDate.text =
                Datetime.formatTimeDate(model.validFrom.split("+")[0], context) + " to " + Datetime.formatTimeDate(
                    model.validTo.split("+")[0],
                    context
                )
            binding.mainLay.setOnClickListener {
                listener.clickingCheckIn(model)
            }
        }
    }

    override fun onCreateViewHolder(viewGroup: ViewGroup, viewType: Int): ViewHolder {
        context = viewGroup.context
        val binding =
            AdapterCheckInPmLayBinding.inflate(LayoutInflater.from(context), viewGroup, false)
        return ViewHolder(binding)
    }

    override fun onBindViewHolder(viewHolder: ViewHolder, position: Int) {
        viewHolder.bind(listFiltered[position], position)
    }

    override fun getItemCount() = listFiltered.size

    @SuppressLint("NotifyDataSetChanged")
    internal fun updateValues(arrayIcons: List<ModelAssignCheckIns>) {
        arrayIconsNew = ArrayList(arrayIcons)
        listFiltered = ArrayList(arrayIcons)
        notifyDataSetChanged()
    }

    internal interface ClickCheckIn {
        fun clickingCheckIn(modelAssignCheckIns: ModelAssignCheckIns)
    }
}

data class ModelAssignCheckIns(
    val validFrom: String,
    val validTo: String,
    val bookingNumber: String,
    val firstName: String,
    val lastName: String,
    val lockName: String,
    val fullName: String,
)

internal fun GuestAssignment.toModel(): ModelAssignCheckIns {
    return ModelAssignCheckIns(
        validFrom = validFrom,
        validTo = validTo,
        bookingNumber = bookingNumber,
        firstName = firstName,
        fullName = fullName,
        lastName = lastName,
        lockName = lockName
    )
}