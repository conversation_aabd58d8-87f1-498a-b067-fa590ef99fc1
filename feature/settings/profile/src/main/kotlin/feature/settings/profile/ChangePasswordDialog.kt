package feature.settings.profile

import android.app.Dialog
import android.content.Context
import android.text.Editable
import android.text.TextWatcher
import android.text.method.HideReturnsTransformationMethod
import android.text.method.PasswordTransformationMethod
import android.view.Window
import android.view.WindowManager
import android.widget.CheckBox
import android.widget.EditText
import android.widget.ImageView
import android.widget.TextView
import com.google.android.material.bottomsheet.BottomSheetDialog
import feature.common.dialogs.appToast
import feature.common.text.isValidPass
import feature.common.text.trimmedTextFieldWatcher
import keyless.feature.settings.profile.R

fun Context.changePassDialog(
    onUpdateButtonClick: (currentPass: String, newPass: String, confirmPass: String) -> Unit
): Dialog {

    val dialog = BottomSheetDialog(this, keyless.feature.common.R.style.trans_bg_dialog)
    dialog.apply {
        requestWindowFeature(Window.FEATURE_NO_TITLE)
        setCancelable(false)
        setContentView(R.layout.changepass_dialog)
        window?.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_STATE_ALWAYS_VISIBLE);

        val confirmPassET = dialog.findViewById<EditText>(R.id.confirmPassET)
        val newPassET = dialog.findViewById<EditText>(R.id.newPassET)
        val currentPassET = dialog.findViewById<EditText>(R.id.currentPassET)
        val check1 = dialog.findViewById<CheckBox>(R.id.check1)
        val check2 = dialog.findViewById<CheckBox>(R.id.check2)
        val check3 = dialog.findViewById<CheckBox>(R.id.check3)
        val showPassCurrentBtn = dialog.findViewById<ImageView>(R.id.showPassCurrentBtn)
        val showPassBtn = dialog.findViewById<ImageView>(R.id.showPassBtn)
        val showPassConfirmBtn = dialog.findViewById<ImageView>(R.id.showPassConfirmBtn)
        val updatePassBtn = dialog.findViewById<TextView>(R.id.updatePassBtn)
        val closeDialogBtn = dialog.findViewById<ImageView>(R.id.closeDialogBtn)

        newPassET?.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {
            }

            override fun onTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {
                val password = p0.toString()
                val letterRegex = Regex("[a-zA-Z]")
                val numericRegex = Regex("\\d")
                check1?.isChecked = newPassET.length() >= 8
                check2?.isChecked = password.contains(letterRegex)
                check3?.isChecked = password.contains(numericRegex)
            }

            override fun afterTextChanged(s: Editable?) {
                val trimmedText = s?.toString()?.trim()
                if (s.toString() != trimmedText) {
                    s?.replace(0, s.length, trimmedText)
                }
            }
        })

        confirmPassET?.addTextChangedListener(trimmedTextFieldWatcher())
        currentPassET?.addTextChangedListener(trimmedTextFieldWatcher())

        showPassCurrentBtn?.setOnClickListener { showHidePass(currentPassET!!, showPassCurrentBtn) }
        showPassBtn?.setOnClickListener { showHidePass(newPassET!!, showPassBtn) }

        showPassConfirmBtn?.setOnClickListener { showHidePass(confirmPassET!!, showPassConfirmBtn) }

        updatePassBtn!!.setOnClickListener {
            if (passResetValid(currentPassET!!, newPassET!!, confirmPassET!!)) {
                val currentPass = currentPassET.text.toString()
                val newPass = newPassET.text.toString()
                val confirmPass = confirmPassET.text.toString()
                onUpdateButtonClick(currentPass, newPass, confirmPass)
            }
        }
        closeDialogBtn!!.setOnClickListener { dialog.dismiss() }
        dialog.show()
    }

    return dialog
}

private fun showHidePass(pass: EditText, btnChange: ImageView) {
    if (pass.transformationMethod.equals(PasswordTransformationMethod.getInstance())) {
        btnChange.setImageResource(keyless.feature.common.R.drawable.ic_eye_show)
        pass.transformationMethod = HideReturnsTransformationMethod.getInstance()
    } else {
        btnChange.setImageResource(keyless.feature.common.R.drawable.ic_eye_hide)
        pass.transformationMethod = PasswordTransformationMethod.getInstance()
    }

    pass.setSelection(pass.text.length) // Move cursor to the end
}

private fun Context.passResetValid(
    currentPassET: EditText,
    newPassET: EditText,
    confirmPassET: EditText
): Boolean {
    val currentPass = currentPassET.text.toString()
    val newPass = newPassET.text.toString()
    val confirmPass = confirmPassET.text.toString()
    if (currentPass.isEmpty()) {
        appToast(getString(keyless.feature.common.R.string.please_enter_current_password))
        return false
    }
    if (newPass.isEmpty()) {
        appToast(getString(keyless.feature.common.R.string.please_enter_new_password))
        return false
    }
    if (newPass.length < 8) {
        appToast(
            getString(
                keyless.feature.common.R.string.the_password_must_be_a_minimum_of_8_characters_with_1_lowercase_and_1_numeric_character
            )
        )
        return false
    }
    if (confirmPass.isEmpty()) {
        appToast(getString(keyless.feature.common.R.string.please_confirm_password))
        return false
    }
    if (newPass != confirmPass) {
        appToast(getString(keyless.feature.common.R.string.confirm_pass_do_not_match))
        return false
    }
    if (currentPass == newPass) {
        appToast(
            getString(keyless.feature.common.R.string.the_current_password_and_the_new_password_cannot_be_the_same)
        )
        return false
    }

    if (!isValidPass(newPass)) {
        appToast(
            getString(
                keyless.feature.common.R.string.the_password_must_be_a_minimum_of_8_characters_with_1_lowercase_and_1_numeric_character
            )
        )
        return false
    }
    if (!isValidPass(confirmPass)) {
        appToast(
            getString(
                keyless.feature.common.R.string.the_password_must_be_a_minimum_of_8_characters_with_1_lowercase_and_1_numeric_character
            )
        )
        return false
    }
    return true
}