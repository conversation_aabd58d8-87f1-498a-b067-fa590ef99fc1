package feature.settings.maintenance.home

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.ComposeView
import androidx.fragment.app.Fragment
import androidx.navigation.fragment.findNavController
import feature.common.compose.theme.AppTheme

class MaintenanceHomeFragment: Fragment() {

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View {
        return ComposeView(inflater.context).apply {
            setContent {
                AppTheme {
                    ScreenContent()
                }
            }
        }
    }

    @Composable
    private fun ScreenContent() {
        MaintenanceHomeScreen(
            modifier = Modifier.fillMaxSize(),
            onBackPress = { requireActivity().onBackPressedDispatcher.onBackPressed() },
            navigator = findNavController()
        )
    }
}