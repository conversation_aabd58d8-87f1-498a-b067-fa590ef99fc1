<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/colorAccent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    tools:context="feature.settings.common.MoreSettingsFragment">

    <ImageView
        android:id="@+id/backBtnSupport"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        style="@style/ImageMirror"
        android:padding="20dp"
        android:layout_marginTop="10dp"
        android:src="@drawable/iv_back_white"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/propertyTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:fontFamily="@font/poppins_medium_500"
        android:text="@string/support"
        android:textColor="@color/white"
        android:textSize="18dp"
        app:layout_constraintBottom_toBottomOf="@+id/backBtnSupport"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/backBtnSupport" />

    <FrameLayout
        android:layout_marginTop="10dp"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:background="@drawable/white_top_corners"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/backBtnSupport"
        app:layout_constraintVertical_bias="0.0">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:elevation="0dp"
            app:cardCornerRadius="32dp"
            app:cardElevation="0dp">


            <TextView
                android:id="@+id/txtValue"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="20dp"
                android:layout_marginTop="28dp"
                android:fontFamily="@font/poppins_semibold_600"
                android:gravity="center"
                android:includeFontPadding="false"
                android:text="@string/find_nearby_device"
                android:textColor="@color/black"
                android:textSize="16dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <View
                android:id="@+id/view"
                android:layout_width="0dp"
                android:layout_height="1dp"
                android:layout_marginTop="18dp"
                android:layout_marginEnd="20dp"
                android:background="@color/line_bg_color"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="@+id/txtValue"
                app:layout_constraintTop_toBottomOf="@+id/txtValue" />

            <View
                android:id="@+id/findNearBy"
                android:layout_width="0dp"
                android:layout_height="0dp"
                app:layout_constraintBottom_toBottomOf="@id/view"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />


            <TextView
                android:id="@+id/txtDiagnosis"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="20dp"
                android:layout_marginTop="15dp"
                android:fontFamily="@font/poppins_semibold_600"
                android:gravity="center"
                android:includeFontPadding="false"
                android:text="@string/diagnostics"
                android:textColor="@color/black"
                android:textSize="16dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/view" />

            <View
                android:id="@+id/viewDiagnosis"
                android:layout_width="0dp"
                android:layout_height="1dp"
                android:layout_marginTop="18dp"
                android:layout_marginEnd="20dp"
                android:background="@color/line_bg_color"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="@+id/txtDiagnosis"
                app:layout_constraintTop_toBottomOf="@+id/txtDiagnosis" />

            <View
                android:id="@+id/clickDiagnosis"
                android:layout_width="0dp"
                android:layout_height="0dp"
                app:layout_constraintBottom_toBottomOf="@+id/viewDiagnosis"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/view" />





            <TextView
                android:id="@+id/txtValue333"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:fontFamily="@font/poppins_semibold_600"
                android:gravity="center"
                android:includeFontPadding="false"
                android:text="@string/contact_keyless_support"
                android:textColor="@color/black"
                app:layout_constraintBottom_toTopOf="@+id/callBtnView"
                app:layout_constraintEnd_toEndOf="@+id/callBtnView"
                app:layout_constraintStart_toStartOf="@+id/callBtnView" />

            <TextView
                android:id="@+id/callBtnTxt"
                android:background="@null"
                style="@style/buttonStyle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:drawableStart="@drawable/call_img"
                android:drawablePadding="15dp"
                android:elevation="1dp"
                android:text="@string/call"
                app:layout_constraintBottom_toBottomOf="@+id/callBtnView"
                app:layout_constraintEnd_toEndOf="@+id/callBtnView"
                app:layout_constraintStart_toStartOf="@+id/callBtnView"
                app:layout_constraintTop_toTopOf="@+id/callBtnView" />

            <View
                android:id="@+id/callBtnView"
                android:layout_width="0dp"
                android:layout_height="50dp"
                android:layout_gravity="bottom"
                android:layout_marginBottom="16dp"
                android:background="@drawable/bg_btn_round"
                android:gravity="center"
                app:layout_constraintBottom_toTopOf="@+id/furtherAssistance"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintWidth_percent="0.6" />


            <TextView
                android:id="@+id/furtherAssistance"
                style="@style/buttonStyle"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="32dp"
                android:layout_marginEnd="32dp"
                android:layout_marginBottom="32dp"
                android:background="@null"
                android:drawablePadding="15dp"
                android:elevation="1dp"
                android:text="@string/if_you_need_further_assistance_you_can_raise_a_ticket_via_the_web_app"
                android:textSize="14dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </FrameLayout>



</androidx.constraintlayout.widget.ConstraintLayout>