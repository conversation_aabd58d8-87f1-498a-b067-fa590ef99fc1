<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:id="@+id/mainLay"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <TextView
        android:layout_marginEnd="10dp"
        android:id="@+id/txtInternalId"
        android:layout_width="0dp"
        style="@style/mirrorText"
        android:textSize="16dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="20dp"
        android:fontFamily="@font/poppins_semibold_600"
        android:textColor="@color/black"
        app:layout_constraintEnd_toStartOf="@+id/txtDate"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/txtLockName"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="1dp"
        android:textSize="14dp"
        style="@style/mirrorText"
        android:layout_marginEnd="16dp"
        android:fontFamily="@font/poppins_regular_400"
        android:textColor="@color/black"
        app:layout_constraintEnd_toStartOf="@+id/dots"
        app:layout_constraintStart_toStartOf="@+id/txtInternalId"
        app:layout_constraintTop_toBottomOf="@+id/txtInternalId" />

    <TextView
        android:id="@+id/txtAssignedBy"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="1dp"
        android:fontFamily="@font/poppins_regular_400"
        android:textSize="14dp"
        style="@style/mirrorText"
        android:textColor="@color/black"
        app:layout_constraintEnd_toEndOf="@+id/txtLockName"
        app:layout_constraintStart_toStartOf="@+id/txtLockName"
        app:layout_constraintTop_toBottomOf="@+id/txtLockName" />

    <TextView
        android:id="@+id/txtAddress"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="1dp"
        style="@style/mirrorText"
        android:fontFamily="@font/poppins_regular_400"
        android:textSize="14dp"
        android:textColor="@color/black"
        app:layout_constraintEnd_toEndOf="@+id/txtLockName"
        app:layout_constraintStart_toStartOf="@+id/txtLockName"
        app:layout_constraintTop_toBottomOf="@+id/txtAssignedBy" />

    <TextView
        android:id="@+id/txtValidDate"
        android:layout_width="0dp"
        style="@style/mirrorText"
        android:layout_height="wrap_content"
        android:fontFamily="@font/poppins_regular_400"
        android:textColor="@color/black"
        android:textSize="12dp"
        app:layout_constraintEnd_toEndOf="@+id/dots"
        app:layout_constraintStart_toStartOf="@+id/txtLockName"
        app:layout_constraintTop_toBottomOf="@+id/txtAddress" />


    <TextView
        android:id="@+id/txtDate"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="20dp"
        style="@style/mirrorText"
        android:fontFamily="@font/poppins_regular_400"
        android:textColor="@color/black"
        android:textSize="10dp"
        app:layout_constraintBottom_toBottomOf="@id/txtInternalId"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


    <ImageView
        android:id="@+id/dots"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:padding="5dp"
        android:src="@drawable/option_dot_img"
        app:layout_constraintBottom_toBottomOf="@+id/txtLockName"
        app:layout_constraintEnd_toEndOf="@+id/txtDate"
        app:layout_constraintTop_toBottomOf="@+id/txtInternalId" />

    <View
        android:id="@+id/view2"
        android:layout_width="0dp"
        android:layout_height="1dp"
        android:layout_marginTop="10dp"
        android:layout_marginEnd="20dp"
        android:layout_marginBottom="10dp"
        android:background="@color/line_bg_color"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="@id/txtInternalId"
        app:layout_constraintTop_toBottomOf="@+id/txtValidDate"
        app:layout_constraintVertical_bias="0.0" />


</androidx.constraintlayout.widget.ConstraintLayout>