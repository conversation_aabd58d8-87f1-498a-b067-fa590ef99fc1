package feature.settings.maintenance

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import domain.common.ErrorHandler
import feature.common.dialogs.appToast
import feature.settings.maintenance.scan.VerifyNearByLockActivity.Companion.VERIFY_NEARBY_LOCK_UNIQUE_KEY
import feature.settings.maintenance.scan.VerifyNearByLockActivity.Companion.VERIFY_NEARBY_LOCK_UNIQUE_REQUEST_CODE
import feature.settings.maintenance.time.ResetTimeAndUnlockActivity
import feature.settings.maintenance.time.ResetTimeAndUnlockViewModel.Companion.RESET_TIME_UNLOCK_LOCK_UNIQUE_KEY
import keyless.feature.settings.databinding.ActivityMaintenanceBinding
import kotlinx.coroutines.launch
import org.koin.android.ext.android.inject


class MaintenanceActivity : AppCompatActivity() {

    lateinit var adapterSharedAccess: AdapterMaintenance
    private lateinit var binding: ActivityMaintenanceBinding
    private val handler: ErrorHandler by inject()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityMaintenanceBinding.inflate(layoutInflater)
        setContentView(binding.root)
        setAdapter()
        clickListeners()
    }

    private fun clickListeners() {
        binding.backBtn.setOnClickListener {
            finish()
        }
    }

    private fun setAdapter() {
        binding.rvMaintenance.layoutManager = LinearLayoutManager(this)
        adapterSharedAccess = AdapterMaintenance(listMaintenance())
        binding.rvMaintenance.adapter = adapterSharedAccess
    }

    private fun listMaintenance(): List<ModelMaintenance> {
        val listMain = ArrayList<ModelMaintenance>()
        var modelMaintenance = ModelMaintenance()
        modelMaintenance.name = getString(keyless.feature.common.R.string.update_battery)
        modelMaintenance.value = "1"
        listMain.add(modelMaintenance)

        modelMaintenance = ModelMaintenance()
        modelMaintenance.name = getString(keyless.feature.common.R.string.firmware_update)
        modelMaintenance.value = "2"
        listMain.add(modelMaintenance)

        return listMain
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        if (resultCode == Activity.RESULT_OK) {
            when (requestCode) {
                VERIFY_NEARBY_LOCK_UNIQUE_REQUEST_CODE -> {

                    lifecycleScope.launch {
                        handler.async(
                            onError = {
                                appToast(it.message ?: "Error happened")
                            }
                        ) {
                            val uniqueKey = data!!.extras!!.getString(VERIFY_NEARBY_LOCK_UNIQUE_KEY)

                            startActivity(
                                Intent(this@MaintenanceActivity, ResetTimeAndUnlockActivity::class.java)
                                    .putExtra(RESET_TIME_UNLOCK_LOCK_UNIQUE_KEY, uniqueKey)
                            )

//                            val modelData = ModelForData()
//                            val lock = Preferences.locks().find {
//                                it.uniqueKey == uniqueKey
//                            } ?: throw Exception("Lock not found")
//
//                            modelData.unique_key = lock.uniqueKey
//                            modelData.access_key = lock.accessKey
//                            modelData.lock_id = lock.id
//                            modelData.owner_id = lock.ownerId
//                            modelData.provider = lock.provider
//                            modelData.encrypted_key = lock.encryptedKey
//                            modelData.internal_id = lock.internalId
//                            modelData.lock_uid = lock.lockUid
//                            startActivity(
//                                Intent(this@MaintenanceActivity, ChangeLockActivity::class.java)
//                                    .putExtra("selectedItem", modelData)
//                            )
                        }
                    }


                }
            }
        }

        super.onActivityResult(requestCode, resultCode, data)
    }
}