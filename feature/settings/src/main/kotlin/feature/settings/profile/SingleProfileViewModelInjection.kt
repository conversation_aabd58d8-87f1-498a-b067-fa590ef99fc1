package feature.settings.profile

import domain.settings.profile.ViewModel
import domain.settings.profile.repositories.ScreenStateRepository
import domain.settings.profile.repositories.SideEffectsRepository
import org.koin.androidx.viewmodel.dsl.viewModel
import org.koin.dsl.module

val singleProfileViewModelInjection = module {
    viewModel {
        val screen = ScreenStateRepository(
            status = get()
        )
        val sideEffects = SideEffectsRepository()
        val viewModel = ViewModel(
            logger = get(),
            status = get(),
            userAccount = get(),
            screenState = screen,
            sideEffects = sideEffects
        )
        ProfileViewModel(
            viewModel = viewModel,
            handler = get(),
            onClear = {}
        )
    }
}