package feature.regula.refactored.checkin.models

import core.common.status.Status
import core.regula.common.models.RegulaImage

sealed interface UIScreenState {
    val status: List<Status>
}

internal data class UILoadingState(
    override val status: List<Status>
) : UIScreenState


internal data class UIReadyState(
    val portrait: RegulaImage,
    val verification: RegulaImage,

    val matchType: MatchType,

    val name: String,
    val expiryDate: String,
    val documentType: String,

    val frontSide: RegulaImage,
    val backSide: RegulaImage?,

    val documentNumber: String,
    val similarity: Double,

    val extra: String,
    val bookingNumber: String,

    override val status: List<Status>
) : UIScreenState {

    val typeInNumber = if (documentType.lowercase() == "passport") "1" else "2"

    val isConfirmEnabled: Boolean = matchType == MatchType.FULL
}

internal enum class MatchType {
    FULL,
    PARTIAL,
    NONE
}

internal fun MatchType.toStatus(): String = when (this) {
    MatchType.FULL -> "1"
    MatchType.PARTIAL -> "0"
    MatchType.NONE -> "3"
}