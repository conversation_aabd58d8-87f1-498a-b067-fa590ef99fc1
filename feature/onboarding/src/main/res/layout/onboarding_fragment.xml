<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <VideoView
        android:id="@+id/onboardVidView"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintHeight_percent="1"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="#3D000000"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.viewpager.widget.ViewPager
        android:id="@+id/onboardViewPager"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />
    <ImageView
        app:layout_constraintHeight_percent="0.15"
        app:layout_constraintVertical_bias="0.06"
        android:layout_width="65dp"
        android:layout_height="65dp"
        android:src="@drawable/keyless_logo"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.google.android.material.tabs.TabLayout
        app:tabRippleColor="@null"
        app:tabMode="fixed"
        app:tabGravity="fill"
        app:tabMaxWidth="30dp"
        app:layout_constraintWidth_percent="0.25"
        app:tabPaddingStart="10dp"
        app:tabPaddingEnd="10dp"
        app:tabIndicatorHeight="0dp"
        android:backgroundTint="@color/transparent"
        app:tabBackground="@drawable/indicator_selector"
        android:id="@+id/tabIndicator"
        android:layout_width="0dp"
        android:layout_height="8dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0.56" />

    <TextView
        android:id="@+id/loginTxt"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:layout_marginStart="60dp"
        android:layout_marginEnd="60dp"
        android:layout_marginBottom="22dp"
        android:background="@drawable/bg_btn_round"
        android:fontFamily="@font/poppins_medium_500"
        android:gravity="center"
        android:includeFontPadding="false"
        android:text="@string/login"
        android:textColor="@color/black"
        android:textSize="16dp"
        app:layout_constraintBottom_toTopOf="@+id/onboardSignBtn"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintWidth_percent="0.6" />

    <TextView
        android:id="@+id/onboardSignBtn"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:fontFamily="@font/poppins_medium_500"
        android:gravity="center"
        android:text="@string/have_account"
        android:textColor="@color/white"
        android:textSize="16dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0.93" />

</androidx.constraintlayout.widget.ConstraintLayout>