package feature.onboarding

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.viewpager.widget.PagerAdapter
import keyless.feature.onboarding.R

internal data class OnBoardModel(val title: String, val subTitle: String)

internal class OnboardAdapter(
    private val context: Context,
) : PagerAdapter() {

    private val onboardData = getTexts()

    private val layoutInflater: LayoutInflater =
        this.context.getSystemService(Context.LAYOUT_INFLATER_SERVICE) as LayoutInflater

    override fun getCount(): Int {
        return onboardData.size
    }

    override fun isViewFromObject(view: View, o: Any): Boolean {
        return view === o as View
    }

    override fun instantiateItem(container: ViewGroup, position: Int): Any {
        val view: View = layoutInflater.inflate(R.layout.onboard_layout, container, false)
        // set data
        val title = onboardData[position].title
        val subtitle = onboardData[position].subTitle
        var onboardTitle = view.findViewById<TextView>(R.id.onboardTitle)
        var onboardSubTitle = view.findViewById<TextView>(R.id.onboardSubTitle)

        onboardTitle.text = title
        onboardSubTitle.text = subtitle

        container.addView(view)
        return view
    }

    override fun destroyItem(container: ViewGroup, position: Int, `object`: Any) {
        container.removeView(`object` as View)
    }

    private fun getTexts() = arrayListOf(
        OnBoardModel(
            context.getString(R.string.access),
            context.getString(R.string.all_your_locks)
        ),
        OnBoardModel(
            context.getString(keyless.feature.common.R.string.unlock),
            context.getString(R.string.anywhere_anytime)
        ),
        OnBoardModel(
            context.getString(R.string.history),
            context.getString(R.string.all_access_history)
        )
    )
}