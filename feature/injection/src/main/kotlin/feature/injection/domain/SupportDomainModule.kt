package feature.injection.domain

import core.locks.manager.LocksManager
import domain.settings.support.ViewModel
import domain.settings.support.models.SupportDomainScope
import org.koin.dsl.module

internal val supportDomainModule = module {
    scope<SupportDomainScope> {
        scoped {
            ViewModel(
                lockLogs = get(),
                logger = get(),
                status = get(),
                manager = get(),
                permissions = get(),
                bluetooth = get()
            )
        }
    }
}