package feature.injection.dashboard

import core.common.status.StatusRepository
import feature.dashboard.home.DashboardHomeAndroidViewModel
import kotlinx.coroutines.flow.combine
import org.koin.core.component.getScopeId
import org.koin.core.module.dsl.viewModel
import org.koin.dsl.module
import presentation.home.dashboard.domain.ViewModel
import presentation.home.dashboard.domain.ScreenData
import presentation.home.dashboard.domain.SideEffect
import presentation.home.dashboard.domain.usecases.UseCases
import presentation.common.domain.repositories.ScreenDataRepository
import presentation.common.domain.repositories.SideEffectsRepository

internal data object DashboardHomeInjectionScope

val dashboardHomeInjection = module {
    scope<DashboardHomeInjectionScope> {
        scoped {
            ScreenDataRepository(initial = ScreenData.empty) {
                combine(it, get<StatusRepository>().stream) { data, status -> data.copy(status = status) }
            }
        }
        scoped { SideEffectsRepository<SideEffect>() }
        scoped {
            UseCases(
                userRepository = get(),
                screenData = get(),
                sideEffects = get(),
                logger = get(),
                status = get(),
                context = get()
            )
        }
        scoped { ViewModel(data = get(), sideEffects = get(), useCases = get()) }
    }

    viewModel {
        val scope = getKoin().getOrCreateScope<DashboardHomeInjectionScope>(DashboardHomeInjectionScope.getScopeId())
        DashboardHomeAndroidViewModel(
            domain = scope.get(),
            dispatchers = get(),
            errorHandler = get(),
            onClear = { scope.close() }
        )
    }
}
