<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:card_view="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/colorAccent"
    tools:context="feature.dashboard.shareaccess.AddUserActivity">

    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">


            <ImageView
                style="@style/ImageMirror"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="@drawable/iv_back_white"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <View
                android:id="@+id/viewBack"
                android:layout_width="30dp"
                android:layout_height="30dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="0.0"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/textView2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:fontFamily="@font/poppins_medium_500"
                android:gravity="center"
                android:text="@string/add_user"
                android:textColor="@color/white"
                android:textSize="18dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />


        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.appcompat.widget.Toolbar>



    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="10dp"
        android:background="@drawable/white_top_corners"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/toolbar"
        app:layout_constraintVertical_bias="0.0">


        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <TextView
                android:id="@+id/tv_enter_email"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginStart="20dp"
                android:layout_marginTop="25dp"
                android:layout_marginEnd="20dp"
                android:fontFamily="@font/poppins_medium_500"
                android:text="@string/enter_email_of_user_you_want_to_add"
                android:textColor="@color/black"
                android:textSize="16sp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />


            <TextView
                android:id="@+id/textView9"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="20dp"
                android:layout_marginTop="20dp"
                android:layout_marginEnd="20dp"
                android:fontFamily="@font/poppins_semibold_600"
                android:text="@string/email"
                android:textColor="@color/black"
                android:textSize="14dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tv_enter_email" />

            <EditText
                android:id="@+id/etEmail"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:background="@drawable/bg_edit_corners"
                android:fontFamily="@font/poppins_medium_500"
                android:hint="@string/enter_email"
                android:layout_marginTop="4dp"
                android:imeOptions="actionSearch"
                android:inputType="textEmailAddress"
                android:minHeight="48dp"
                android:padding="10dp"
                style="@style/mirrorText"
                android:textColor="@color/black"
                android:textSize="16dp"
                app:layout_constraintEnd_toEndOf="@+id/textView9"
                app:layout_constraintHorizontal_bias="0.0"
                app:layout_constraintStart_toStartOf="@+id/textView9"
                app:layout_constraintTop_toBottomOf="@+id/textView9" />

            <ImageView
                android:id="@+id/searchBtn"
                android:paddingEnd="16dp"
                android:paddingStart="10dp"
                android:layout_width="wrap_content"
                android:layout_height="0dp"
                android:src="@drawable/iv_search"
                app:layout_constraintBottom_toBottomOf="@+id/etEmail"
                app:layout_constraintEnd_toEndOf="@+id/etEmail"
                app:layout_constraintTop_toBottomOf="@+id/textView9" />


            <LinearLayout
                android:id="@+id/linearResults"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="20dp"
                android:layout_marginTop="24dp"
                android:layout_marginEnd="20dp"
                android:orientation="vertical"
                android:visibility="visible"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/etEmail"
                app:layout_constraintVertical_bias="0.0">

                <TextView
                    android:id="@+id/txtNotExists"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/poppins_regular_400"
                    android:text="@string/this_user_does_not_exist_and_an_invite_email_will_be_sent_to_him_her"
                    android:textColor="@color/black"
                    android:textSize="14dp"
                    android:visibility="gone" />

                <TextView
                    android:id="@+id/alreadyShared"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/poppins_semibold_600"
                    android:includeFontPadding="false"
                    android:text="@string/already_shared"
                    android:textColor="@color/black"
                    android:textSize="14dp"
                    android:visibility="gone" />

                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/userDetails"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="4dp"
                    android:layout_marginTop="25dp"
                    android:layout_marginEnd="4dp"
                    android:layout_marginBottom="8dp"
                    android:theme="@style/Theme.MaterialComponents.Light"
                    android:visibility="gone"
                    card_view:cardBackgroundColor="@color/card_bg_color"
                    card_view:cardCornerRadius="10dp"
                    card_view:cardElevation="0dp"
                    card_view:cardMaxElevation="0dp"
                    card_view:strokeColor="@color/card_stroke_access"
                    card_view:strokeWidth="1dp">

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">


                        <TextView
                            android:id="@+id/txtName"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="16dp"
                            android:layout_marginTop="16dp"
                            android:fontFamily="@font/poppins_semibold_600"
                            android:includeFontPadding="false"
                            android:textColor="@color/black"
                            android:textSize="14dp"
                            card_view:layout_constraintStart_toStartOf="parent"
                            card_view:layout_constraintTop_toTopOf="parent" />

                        <TextView
                            android:id="@+id/txtEmail"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="16dp"
                            android:layout_marginTop="4dp"
                            android:layout_marginBottom="16dp"
                            android:fontFamily="@font/poppins_regular_400"
                            android:includeFontPadding="false"
                            android:textColor="@color/black"
                            android:textSize="14dp"
                            android:visibility="visible"
                            card_view:layout_constraintBottom_toBottomOf="parent"
                            card_view:layout_constraintStart_toStartOf="parent"
                            card_view:layout_constraintTop_toBottomOf="@+id/txtName"
                            card_view:layout_constraintVertical_bias="0.0" />

                        <TextView
                            android:id="@+id/txtPhone"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="16dp"
                            android:layout_marginTop="4dp"
                            android:layout_marginBottom="16dp"
                            android:fontFamily="@font/poppins_regular_400"
                            android:includeFontPadding="false"
                            android:textColor="@color/black"
                            android:textSize="14dp"
                            android:visibility="visible"
                            card_view:layout_constraintBottom_toBottomOf="parent"
                            card_view:layout_constraintStart_toStartOf="parent"
                            card_view:layout_constraintTop_toBottomOf="@+id/txtEmail"
                            card_view:layout_constraintVertical_bias="0.0" />


                        <TextView
                            android:id="@+id/txtDateRange"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="16dp"
                            android:layout_marginTop="4dp"
                            android:layout_marginBottom="16dp"
                            android:fontFamily="@font/poppins_regular_400"
                            android:includeFontPadding="false"
                            android:textColor="@color/black"
                            android:textSize="14dp"
                            card_view:layout_constraintBottom_toBottomOf="parent"
                            card_view:layout_constraintStart_toStartOf="parent"
                            card_view:layout_constraintTop_toBottomOf="@+id/txtPhone"
                            card_view:layout_constraintVertical_bias="0.0" />

                        <TextView
                                android:id="@+id/txtPasscode"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="16dp"
                                android:layout_marginTop="4dp"
                                android:layout_marginBottom="16dp"
                                android:fontFamily="@font/poppins_regular_400"
                                android:includeFontPadding="false"
                                android:textColor="@color/black"
                                android:textSize="14dp"
                                card_view:layout_constraintBottom_toBottomOf="parent"
                                card_view:layout_constraintStart_toStartOf="parent"
                                card_view:layout_constraintTop_toBottomOf="@+id/txtDateRange"
                                card_view:layout_constraintVertical_bias="0.0" />


                    </androidx.constraintlayout.widget.ConstraintLayout>

                </com.google.android.material.card.MaterialCardView>


            </LinearLayout>

            <TextView
                android:id="@+id/textView12"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="20dp"
                android:layout_marginTop="30dp"
                android:fontFamily="@font/poppins_semibold_600"
                android:text="@string/routine"
                android:textColor="@color/black"
                android:textSize="14dp"
                android:visibility="gone"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/linearResults" />

            <EditText
                android:id="@+id/etRoutine"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginEnd="20dp"
                android:visibility="gone"
                android:layout_marginTop="4dp"
                style="@style/mirrorText"
                android:background="@drawable/bg_edit_corners"
                android:fontFamily="@font/poppins_medium_500"
                android:hint="@string/enter_routine"
                android:clickable="true"
                android:editable="false"
                android:focusable="false"
                android:minHeight="48dp"
                android:padding="10dp"
                android:text="Default"
                android:textColor="@color/black"
                android:textSize="16dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="@+id/textView12"
                app:layout_constraintTop_toBottomOf="@+id/textView12" />

            <TextView
                android:id="@+id/textView14"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="20dp"
                android:layout_marginTop="15dp"
                android:fontFamily="@font/poppins_semibold_600"
                android:text="@string/start_date"
                android:textColor="@color/black"
                android:textSize="14dp"
                android:visibility="gone"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/etRoutine" />

            <EditText
                android:id="@+id/etStartDate"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginEnd="20dp"
                android:layout_marginTop="4dp"
                android:background="@drawable/bg_edit_corners"
                android:fontFamily="@font/poppins_medium_500"
                android:hint="@string/enter_start_date"
                android:minHeight="48dp"
                android:visibility="gone"
                android:clickable="true"
                style="@style/mirrorText"
                android:editable="false"
                android:focusable="false"
                android:padding="10dp"
                android:textColor="@color/black"
                android:textSize="16dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="@+id/textView14"
                app:layout_constraintTop_toBottomOf="@+id/textView14" />

            <TextView
                android:id="@+id/textView16"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="20dp"
                android:layout_marginTop="15dp"
                android:fontFamily="@font/poppins_semibold_600"
                android:text="@string/end_date"
                android:textColor="@color/black"
                android:textSize="14dp"
                android:visibility="gone"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/etStartDate" />

            <EditText
                android:id="@+id/etEndDate"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginEnd="20dp"
                android:background="@drawable/bg_edit_corners"
                android:fontFamily="@font/poppins_medium_500"
                android:hint="@string/enter_end_date"
                android:minHeight="48dp"
                android:layout_marginTop="4dp"
                android:padding="10dp"
                android:visibility="gone"
                style="@style/mirrorText"
                android:textColor="@color/black"
                android:clickable="true"
                android:editable="false"
                android:focusable="false"
                android:textSize="16dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="@+id/textView16"
                app:layout_constraintTop_toBottomOf="@+id/textView16" />


            <TextView
                android:layout_width="wrap_content"
                android:text="@string/check_in_required"
                android:id="@+id/txtCheckInReq"
                android:textColor="@color/black"
                android:layout_marginTop="20dp"
                android:visibility="gone"
                android:textSize="16dp"
                android:layout_marginStart="16dp"
                android:fontFamily="@font/poppins_semibold_600"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/etEndDate"
                android:layout_height="wrap_content"/>


            <ImageView
                android:visibility="gone"
                android:paddingEnd="10dp"
                android:id="@+id/infoBtn"
                android:layout_width="wrap_content"
                android:layout_height="0dp"
                android:padding="4dp"
                android:src="@drawable/ic_baseline_info_24"
                app:layout_constraintBottom_toBottomOf="@+id/txtCheckInReq"
                app:layout_constraintStart_toEndOf="@+id/txtCheckInReq"
                app:layout_constraintTop_toTopOf="@+id/txtCheckInReq" />


            <androidx.appcompat.widget.SwitchCompat
                android:id="@+id/switchCheckIn"
                style="@style/ImageMirror"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:checked="false"
                android:textSize="15sp"
                app:backgroundColorOnSwitchOff="@color/color_grey"
                app:backgroundColorOnSwitchOn="@color/colorAccent"
                app:layout_constraintBottom_toBottomOf="@+id/txtCheckInReq"
                app:layout_constraintEnd_toEndOf="parent"
                android:layout_marginEnd="16dp"
                android:visibility="gone"
                app:layout_constraintTop_toTopOf="@+id/txtCheckInReq"
                app:showText="false"
                app:strokeColorOnSwitchOff="@color/colorAccent"
                app:strokeColorOnSwitchOn="@color/colorAccent"
                app:textColorOnSwitchOff="@color/white"
                app:textColorOnSwitchOn="@color/colorAccent"
                app:thumbColorOnSwitchOff="#FFFFFF"
                app:thumbColorOnSwitchOn="#FFFFFF" />

            <TextView
                    android:layout_width="wrap_content"
                    android:text="@string/generate_passcode"
                    android:id="@+id/txtGeneratePasscode"
                    android:textColor="@color/black"
                    android:layout_marginTop="20dp"
                    android:visibility="gone"
                    android:textSize="16dp"
                    android:layout_marginStart="16dp"
                    android:fontFamily="@font/poppins_semibold_600"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/switchCheckIn"
                    android:layout_height="wrap_content"/>


            <ImageView
                    android:visibility="gone"
                    android:paddingEnd="10dp"
                    android:id="@+id/passcodeInfoBtn"
                    android:layout_width="wrap_content"
                    android:layout_height="0dp"
                    android:padding="4dp"
                    android:src="@drawable/ic_baseline_info_24"
                    app:layout_constraintBottom_toBottomOf="@+id/txtGeneratePasscode"
                    app:layout_constraintStart_toEndOf="@+id/txtGeneratePasscode"
                    app:layout_constraintTop_toTopOf="@+id/txtGeneratePasscode"/>

            <androidx.appcompat.widget.SwitchCompat
                    android:id="@+id/switchPasscode"
                    style="@style/ImageMirror"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:checked="false"
                    android:textSize="15sp"
                    app:backgroundColorOnSwitchOff="@color/color_grey"
                    app:backgroundColorOnSwitchOn="@color/colorAccent"
                    app:layout_constraintBottom_toBottomOf="@+id/txtGeneratePasscode"
                    app:layout_constraintEnd_toEndOf="parent"
                    android:layout_marginEnd="16dp"
                    android:visibility="gone"
                    app:layout_constraintTop_toTopOf="@+id/txtGeneratePasscode"
                    app:showText="false"
                    app:strokeColorOnSwitchOff="@color/colorAccent"
                    app:strokeColorOnSwitchOn="@color/colorAccent"
                    app:textColorOnSwitchOff="@color/white"
                    app:textColorOnSwitchOn="@color/colorAccent"
                    app:thumbColorOnSwitchOff="#FFFFFF"
                    app:thumbColorOnSwitchOn="#FFFFFF" />


            <TextView
                android:id="@+id/btnSave"
                android:layout_width="0dp"
                android:layout_height="50dp"
                android:layout_marginStart="60dp"
                android:layout_marginEnd="60dp"
                android:layout_marginBottom="60dp"
                android:layout_marginTop="60dp"
                android:visibility="gone"
                android:includeFontPadding="false"
                android:background="@drawable/bg_btn_round"
                android:fontFamily="@font/poppins_regular_400"
                android:gravity="center"
                android:text="@string/shared_access"
                android:textColor="@color/black"
                android:textSize="16dp"
                app:layout_constraintTop_toBottomOf="@+id/switchCheckIn"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent" />


        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.core.widget.NestedScrollView>


</androidx.constraintlayout.widget.ConstraintLayout>