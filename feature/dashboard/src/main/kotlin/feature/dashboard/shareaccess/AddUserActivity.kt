package feature.dashboard.shareaccess

import android.annotation.SuppressLint
import android.app.Activity
import com.wdullaer.materialdatetimepicker.date.DatePickerDialog
import android.content.Intent
import android.os.Build
import android.os.Bundle
import android.util.Log
import android.view.View
import android.view.inputmethod.EditorInfo
import android.widget.TextView.OnEditorActionListener
import androidx.activity.result.ActivityResultCallback
import androidx.activity.result.contract.ActivityResultContracts
import androidx.annotation.RequiresApi
import androidx.appcompat.app.AppCompatActivity
import androidx.core.view.isVisible
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import com.google.gson.JsonArray
import com.google.gson.JsonObject
import com.wdullaer.materialdatetimepicker.time.TimePickerDialog
import core.lock.ttlock.AndroidTTLockManager
import core.lock.ttlock.TTLockManager
import core.lock.ttlock.models.AndroidTTLock
import data.common.preferences.Preferences
import data.network.android.LocksListResponse
import data.network.android.RoutineModel
import data.network.android.UserSharedModel
import data.utils.android.CommonValues
import data.utils.android.CommonValues.Companion.GUEST
import data.utils.android.CommonValues.Companion.INTEGRATOR
import data.utils.android.datetime.isInTTLockPasscodeRange
import data.utils.android.datetime.toCalendar
import data.utils.android.datetime.toDate
import feature.common.dialogs.OnActionOK
import feature.common.dialogs.ProgressDialogUtils
import feature.common.dialogs.defaultDialog
import data.utils.android.hideKeyboard
import data.utils.android.isValidEmail
import data.utils.android.settings.SharedPreferenceUtils
import data.utils.android.toast
import feature.common.device.Datetime
import feature.common.dialogs.appToast
import feature.common.dialogs.scanningDialog
import feature.common.text.noSpaceTextFieldWatcher
import feature.routines.SelectRoutineActivity
import keyless.feature.dashboard.R
import keyless.feature.dashboard.databinding.ActivityAddUserBinding
import kotlinx.coroutines.CancellationException
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.launch
import org.koin.android.ext.android.inject
import java.text.DateFormatSymbols
import java.text.ParseException
import java.text.SimpleDateFormat
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.ZoneId
import java.time.ZoneOffset
import java.time.format.DateTimeFormatter
import java.util.Calendar
import java.util.Date
import java.util.Locale
import java.util.TimeZone
import kotlin.time.Duration.Companion.seconds

class AddUserActivity : AppCompatActivity() {
    private val ttlock: TTLockManager by inject()

    private var detail: UserSharedModel = UserSharedModel()
    private var checkInValue: Boolean = false
    private var passcodeValue: Boolean = false
    private var checkInValueApi: Boolean = false
    private var passcodeValueApi: Boolean = false
    private var timeForCalender: String = ""
    private var idForUserId: String = ""
    private var lock_id_edit: String = ""
    private var newStartDatedate: String = ""
    private var endDateApi: String = ""
    private var startDateApi: String = ""
    private var userOnlyDetail = UserSharedModel()
    private var routine_id = "forever"
    private lateinit var lockDetails: LocksListResponse.LocksModel
    private var isSupportPasscode = false
    lateinit var mViewModel: AddUserViewModel
    private val sharePrefs: SharedPreferenceUtils by lazy {
        SharedPreferenceUtils.getInstance(
            this
        )
    }
    var whichApiToHit = ""
    private var deviceName: String = ""
    private lateinit var binding: ActivityAddUserBinding

    val startActivityIntent = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult(),
        ActivityResultCallback() {
            if (it.resultCode == RESULT_OK) {
                val dataMain = it?.data?.getParcelableExtra<RoutineModel>("routine")
                dataMain?.let {
                    binding.etRoutine.setText(dataMain?.name)
                    routine_id = dataMain._id
                    Log.e("routine_id", routine_id)
                }
//            performSearch()
            }
            // Add same code that you want to add in onActivityResult method
        })


    @RequiresApi(Build.VERSION_CODES.O)
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityAddUserBinding.inflate(layoutInflater)
        setContentView(binding.root)
        initz()
        deviceName = CommonValues.getDeviceNameApi()
        onClickEvents()
        observerInit()
    }


    private fun observerInit() {
        mViewModel.getCompanyProfileResponse.observe(this) {
            checkInValue = it.data!!.checkin
            passcodeValue = runCatching { detail.assignment_data.passcodeId.isNotEmpty() }.getOrDefault(false)
            Log.e("checking1", checkInValueApi.toString())
            if (intent.hasExtra("detailForEdit")) {

                setData()
            }
            onClickEvents()

        }


        mViewModel.progress.observe(this) {
            if (it) {
                ProgressDialogUtils.getInstance().hideProgress()
                ProgressDialogUtils.getInstance().showProgress(this, true)
            } else {
                ProgressDialogUtils.getInstance().hideProgress()
            }
        }

        mViewModel.error.observe(this) {
            toast(it)
        }


    }

    private fun setData() {
        if (intent.hasExtra("detailForEdit")) {
            detail = intent.getParcelableExtra<UserSharedModel>("detailForEdit")!!
            binding.tvEnterEmail.isVisible = false
            binding.textView9.isVisible = false
            binding.etEmail.isVisible = false
            binding.txtDateRange.isVisible = false
            if (Preferences.userRole.get() == GUEST) {
                binding.textView12.isVisible = false
                binding.etRoutine.isVisible = false
            } else {
                binding.textView12.isVisible = true
                binding.etRoutine.isVisible = true
            }
            binding.userDetails.isVisible = true
            binding.textView14.isVisible = true
            binding.etStartDate.isVisible = true
            binding.textView16.isVisible = true
            binding.etEndDate.isVisible = true
            if (detail!!.assignment_data.time_profile_id.name == "forever_routine") {
                binding.etRoutine.setText("Default")
            } else {
                binding.etRoutine.setText(detail.assignment_data.time_profile_id.name)
            }

            if (detail.assignment_data.passcodeId.isNotEmpty()) {
                binding.txtPasscode.isVisible = true
                binding.txtPasscode.text = getString(R.string.passcode) + ": " + detail.assignment_data.passcode
            } else {
                binding.txtPasscode.visibility = View.GONE
            }

            binding.etStartDate.setText(
                CommonValues.formatDateEdit(
                    detail.assignment_data.valid_from
                )
            )
            binding.etEndDate.setText(
                CommonValues.formatDateEdit(
                    detail.assignment_data.valid_to
                )
            )
            binding.txtName.text = detail.detail.first_name + " " + detail.detail.last_name
            binding.txtEmail.text = detail.detail.email
            binding.txtPhone.text = detail.detail.country_code + " " + detail.detail.mobile_number
            binding.textView2.text = getString(keyless.data.utils.android.R.string.edit_access)
            binding.btnSave.isVisible = true
            binding.btnSave.text = getString(keyless.data.utils.android.R.string.update_access)
            binding.switchCheckIn.isChecked = detail.assignment_data.checkin
            binding.switchPasscode.isChecked = detail.assignment_data.passcodeId.isNotEmpty()
            checkInValueApi = detail.assignment_data.checkin
            passcodeValueApi = detail.assignment_data.passcodeId.isNotEmpty()
            startDateApi = detail.assignment_data.valid_from
            timeForCalender = detail.assignment_data.valid_from
            endDateApi = detail.assignment_data.valid_to
            routine_id = detail.assignment_data.time_profile_id._id
            lock_id_edit = detail.assignment_data.lock_id
            idForUserId = detail.assignment_data._id
            if (detail.detail.isSecondryGuest) {
                binding.switchCheckIn.isVisible = false
                binding.txtCheckInReq.isVisible = false
                binding.infoBtn.isVisible = false
                binding.switchPasscode.isVisible = isSupportPasscode
                binding.txtGeneratePasscode.isVisible = isSupportPasscode
                binding.passcodeInfoBtn.isVisible = isSupportPasscode

            } else {
                if (checkInValue) {
                    binding.switchCheckIn.isVisible = true
                    binding.txtCheckInReq.isVisible = true
                    binding.infoBtn.isVisible = true
                } else {
                    binding.switchCheckIn.isVisible = false
                    binding.txtCheckInReq.isVisible = false
                    binding.infoBtn.isVisible = false
                }

                binding.switchPasscode.isVisible = isSupportPasscode
                binding.txtGeneratePasscode.isVisible = isSupportPasscode
                binding.passcodeInfoBtn.isVisible = isSupportPasscode

//                binding.switchCheckIn.isVisible = true
//                binding.txtCheckInReq.isVisible = true
//                binding.infoBtn.isVisible = true
            }


        }

    }

    private fun initz() {
        mViewModel = ViewModelProvider(this)[AddUserViewModel::class.java]
        if (Preferences.userRole.get() != GUEST) {
            mViewModel.getCompanyProfile(SharedPreferenceUtils.getInstance(this).token)
        }
        lockDetails = intent.getParcelableExtra("lockDetails")!!
        isSupportPasscode = lockDetails.lock.provider == CommonValues.oji ||
                lockDetails.lock.provider == CommonValues.linko ||
                lockDetails.lock.provider == CommonValues.tedee
        binding.etEmail.addTextChangedListener(noSpaceTextFieldWatcher())
        setData()

    }

//    override fun onResume() {
//        super.onResume()
//        binding.searchBtn.callOnClick()
//    }

    @RequiresApi(Build.VERSION_CODES.O)
    private fun onClickEvents() {
        binding.switchCheckIn.setOnCheckedChangeListener { buttonView, isChecked ->
            Log.e("checking", isChecked.toString())
            if (binding.switchCheckIn.isPressed) {
                Log.e("checking1", isChecked.toString())
                checkInValueApi = isChecked
            } else {
                checkInValueApi = checkInValue
                Log.e("checking1", checkInValueApi.toString())
            }
        }

        binding.switchPasscode.setOnCheckedChangeListener { buttonView, isChecked ->
            Log.e("checking", isChecked.toString())

            if (binding.switchPasscode.isPressed) {
                Log.e("checking1", isChecked.toString())
                passcodeValueApi = isChecked
            } else {
                passcodeValueApi = passcodeValue
                Log.e("checking1", passcodeValueApi.toString())
            }
        }




        binding.searchBtn.setOnClickListener {
            if (binding.etEmail.text.toString().isEmpty()) {
                toast(getString(keyless.feature.common.R.string.please_enter_your_email))
            } else if (!binding.etEmail.text.toString().trim().isValidEmail()) {
                toast(getString(keyless.feature.common.R.string.please_enter_correct_email))
            } else {
                performSearch()
            }

        }
        binding.etRoutine.setOnClickListener {
            if (Preferences.userRole.get() == INTEGRATOR && Build.VERSION.SDK_INT == Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
                startActivityIntent.launch(Intent(this, SelectRoutineActivity::class.java))
            } else {
                startActivityForResult(Intent(this, SelectRoutineActivity::class.java), 15)
            }
        }

        binding.etEmail.setOnEditorActionListener(OnEditorActionListener { v, actionId, event ->
            if (actionId == EditorInfo.IME_ACTION_SEARCH) {
                if (binding.etEmail.text.toString().isEmpty()) {
                    toast(getString(keyless.feature.common.R.string.please_enter_your_email))
                } else if (!binding.etEmail.text.toString().trim().isValidEmail()) {
                    toast(getString(keyless.feature.common.R.string.please_enter_correct_email))
                } else {
                    performSearch()
                }
                return@OnEditorActionListener true
            }
            false
        })

        binding.etStartDate.setOnClickListener {
            pickDateTime(1)
        }

        binding.etEndDate.setOnClickListener {
            if (binding.etStartDate.text.toString().trim().isEmpty()) {
                toast(getString(keyless.data.utils.android.R.string.please_select_start_time_first))
            } else {
                binding.etEndDate.isEnabled = false
                pickDateTime(2)
            }

        }

        binding.infoBtn.setOnClickListener {
            defaultDialog(
                this,
                getString(
                    keyless.data.utils.android.R.string.enabling_this_feature_will_initiate_a_check_in_process_for_the_guest_to_upload_their_passport_documents_and_complete_a_facial_verification // ktlint-disable max-line-length
                ),
                object : OnActionOK {
                    override fun onClickData() {

                    }
                })
        }

        binding.passcodeInfoBtn.setOnClickListener {
            val resource = if (lockDetails.ttlockOfflinePasscode()) {
                keyless.data.utils.android.R.string.generate_offline_passcode_helper_text
            } else {
                keyless.data.utils.android.R.string.generate_passcode_helper_text
            }
            defaultDialog(
                this,
                getString(
                    resource
                ),
                object : OnActionOK {
                    override fun onClickData() {

                    }
                })
        }

        binding.btnSave.setOnClickListener {
            if (intent.hasExtra("detailForEdit")) {
                if (validation()) {
                    val jsonObject = JsonObject()
                    jsonObject.addProperty("routine_id", routine_id)
                    jsonObject.addProperty("valid_from", startDateApi)
                    jsonObject.addProperty("valid_to", endDateApi)
                    jsonObject.addProperty("lock_id", lock_id_edit)
                    jsonObject.addProperty("checkin", checkInValueApi)
                    jsonObject.addProperty("isPasscode", passcodeValueApi)

                    editAccess(jsonObject)
                }

            } else {
                if (whichApiToHit == "inviteUser") {
                    if (validation()) {
                        val jsonObjectMain = JsonObject()
                        val jsonArray = JsonArray()
                        val jsonObject = JsonObject()
                        jsonObject.addProperty("routine_id", routine_id)
                        jsonObject.addProperty("valid_from", startDateApi)
                        jsonObject.addProperty("valid_to", endDateApi)
                        jsonObject.addProperty("checkin", checkInValueApi)
                        jsonObject.addProperty("isPasscode", passcodeValueApi)
                        jsonObject.addProperty(
                            "lock_id",
                            lockDetails.assignment?.assignment_data?.lock_id
                        )
                        jsonArray.add(jsonObject)
                        jsonObjectMain.add("lock_data", jsonArray)
                        jsonObjectMain.addProperty("isPasscode", passcodeValueApi)
                        mViewModel.hitInviteApi(
                            sharePrefs.token,
                            jsonObjectMain,
                            binding.etEmail.text.toString()
                        ).observe(this) {
                            if (it.success) {
                                setResult(Activity.RESULT_OK)
                                finish()
                            } else {
                                defaultDialog(
                                    this,
                                    it.message,
                                    object : OnActionOK {
                                        override fun onClickData() {
                                        }
                                    })
                            }
                        }
                    }
                } else {
                    if (validation()) {
                        val jsonObjectMain = JsonObject()
                        val jsonArray = JsonArray()
                        val jsonObject1 = JsonObject()
                        val jsonObject = JsonObject()
                        val jsonArray1 = JsonArray()
                        jsonObject.addProperty("routine_id", routine_id)
                        jsonObject.addProperty("checkin", checkInValueApi)
                        jsonObject.addProperty("valid_from", startDateApi)
                        jsonObject.addProperty("valid_to", endDateApi)
                        jsonObject.addProperty(
                            "lock",
                            lockDetails.assignment?.assignment_data?.lock_id
                        )
                        jsonArray.add(jsonObject)
                        jsonObject1.addProperty("urn", userOnlyDetail.detail.urn_id)
                        jsonObject1.addProperty("userId", userOnlyDetail.detail._id)
                        jsonArray1.add(jsonObject1)
                        jsonObjectMain.add("locks", jsonArray)
                        jsonObjectMain.add("urns", jsonArray1)
                        jsonObjectMain.addProperty("isPasscode", passcodeValueApi)
                        mViewModel.hitShareAccess(sharePrefs.token, jsonObjectMain).observe(this) {
                            if (it.success) {
                                mViewModel.hitLogApi(
                                    sharePrefs.token,
                                    lockDetails.lock._id,
                                    userOnlyDetail.detail._id,
                                    deviceName,
                                    sharePrefs.uuid,
                                    "1",
                                    startDateApi,
                                    endDateApi,
                                    it.assignment_id,
                                ).observe(this) {
                                    setResult(Activity.RESULT_OK)
                                    finish()
                                }
                            } else {
                                defaultDialog(
                                    this,
                                    it.message,
                                    object : OnActionOK {
                                        override fun onClickData() {
                                            finish()
                                        }
                                    })
                            }
                        }
                    }
                }
            }
        }

        binding.viewBack.setOnClickListener {
            finish()
        }


    }

    private fun convertToUtc2(startDateApi: Date): String {
        val calendar = Calendar.getInstance()
        calendar.timeZone = TimeZone.getTimeZone(Preferences.timeZoneName.get())
        calendar.time = startDateApi
//        val time = calendar.time
        val finalCalender = SimpleDateFormat("yyyy-MM-dd'T'HH:mm", Locale("en"))
        finalCalender.timeZone = TimeZone.getTimeZone("UTC")
        val format2 = finalCalender.format(calendar.time)
        return "$format2:00+0000"
    }


    private fun validation(): Boolean {
        val message = """
            Passcode is Enabled 
            The Day and Time for the Start and End Date should be the same!

            Example
            Start date: 01.01.2025 00.00
            End date: 01.01.2026 00.00
        """.trimIndent()
        if (binding.etStartDate.text.isEmpty()) {
            toast(getString(keyless.data.utils.android.R.string.please_enter_start_date))
            return false
        }

        if (binding.etEndDate.text.isEmpty()) {
            toast(getString(keyless.data.utils.android.R.string.please_enter_end_date))
            return false
        }

        if (binding.etEndDate.text.isNotBlank()) {
            val startDate = Datetime.fromString(startDateApi)?.toCalendar()?.toDate()
            val endDate = Datetime.fromString(endDateApi)?.toCalendar()?.toDate()
            if (endDate == null || startDate == null) {
                defaultDialog(this, "Date error") { }
                return false
            }
            if (enforceTTLockTime() && !endDate.isInTTLockPasscodeRange(startDate)) {
                defaultDialog(this, message) { }
                return false
            }

            if (enforceTTLockTime() && endDate.isAfter(startDate.plusYears(3))) {
                val message = """
                    Passcode is Enabled 
                    Access duration cannot be more than 3 years.
                 """.trimIndent()
                defaultDialog(this, message) { }
                return false
            }
        }

        val dateStart = CommonValues.formateCompare(
            binding.etStartDate.text.toString()
        )
        val dateEnd = CommonValues.formateCompare(
            binding.etEndDate.text.toString().replace("\"", "")
        )

        try {
            val formatter = SimpleDateFormat("dd-MM-yyyy HH:mm", Locale("en"))
            val str1 = dateStart
            val date1 = formatter.parse(str1)
            val str2 = dateEnd
            val date2 = formatter.parse(str2)
            if (date1 > date2) {
                toast(getString(keyless.data.utils.android.R.string.end_date_time_should_be_greater_than_start_date))
                return false
            } else if (date1 == date2) {
                toast(getString(keyless.data.utils.android.R.string.start_time_end_time_should_not_be_the_same))
                return false
            } else {
                return true
            }
        } catch (e1: ParseException) {
            e1.printStackTrace()
        }


        return true
    }

    private fun pickDateTime(value: Int) {
        val currentDateTime = Calendar.getInstance()
        currentDateTime.timeZone = TimeZone.getTimeZone(Preferences.timeZoneName.get())
        val startYear = currentDateTime.get(Calendar.YEAR)
        val startMonth = currentDateTime.get(Calendar.MONTH)
        val startDay = currentDateTime.get(Calendar.DAY_OF_MONTH)
        val startHour = currentDateTime.get(Calendar.HOUR_OF_DAY)
        val startMinute = currentDateTime.get(Calendar.MINUTE)
        val currentDateTime1 = LocalDateTime.now(ZoneOffset.UTC)
        val formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'hh:mm:ss", Locale("en"))
        val formattedDateTime = currentDateTime1.format(formatter)
        try {


            var datePickerDialog = DatePickerDialog.newInstance(
                { _, year, month, day ->
                    val tpd: TimePickerDialog = TimePickerDialog.newInstance(
                        { view, hourOfDay, minute, second ->
                            val pickedDateTime = Calendar.getInstance()

                            pickedDateTime.timeZone = TimeZone.getTimeZone(Preferences.timeZoneName.get())

                            pickedDateTime.set(
                                year,
                                month,
                                day,
                                hourOfDay,
                                if (lockDetails.ttlockOfflinePasscode()) 0 else minute
                            )
                            var mMonth = (month + 1).toString()
                            var mDay = (day).toString()
                            var mHour = (hourOfDay).toString()
                            var mMinute = if (lockDetails.ttlockOfflinePasscode()) "0" else minute.toString()

                            if (mMonth.length == 1) {
                                mMonth = "0$mMonth"
                            }
                            if (mDay.length == 1) {
                                mDay = "0$mDay"
                            }
                            if (mHour.length == 1) {
                                mHour = "0$mHour"
                            }
                            if (mMinute.length == 1) {
                                mMinute = "0$mMinute"
                            }

                            val month = DateFormatSymbols.getInstance(Locale("en")).shortMonths[mMonth.toInt() - 1]
                            //                            this.setText("$mDay-$month-${year} ${mHour}:${mMinute}")
                            if (value == 1) {
                                binding.etStartDate.setText("$mDay-$month-${year} ${mHour}:${mMinute}")
                                val date2 = pickedDateTime.time
                                timeForCalender = convertToUtc(date2)
                                startDateApi = convertToUtc2(date2)
                                Log.d("// startDateApi", convertToUtc2(date2))
                            } else {
                                binding.etEndDate.setText("$mDay-$month-${year} ${mHour}:${mMinute}")
                                val date2 = pickedDateTime.time
                                endDateApi = convertToUtc2(date2)
                                Log.d("// endDateApi", convertToUtc2(date2))
                                //                            endDateApi = convertToUtc2(date2)
                                //                            Log.d("// date two" + convertToUtc2(date2), "")
                            }
                        },
                        startHour,
                        startMinute,
                        false
                    )

                    tpd.enableMinutes(!lockDetails.ttlockOfflinePasscode())
                    tpd.enableSeconds(false)


                    val dateValidFrom = CommonValues.dateFor(
                        lockDetails.assignment?.assignment_data?.valid_from!!.split(".")[0]
                    )
                    val dateValidTo =
                        CommonValues.dateFor(
                            lockDetails.assignment?.assignment_data?.valid_to!!.split(
                                "."
                            )[0]
                        )
                    val hour = CommonValues.dateForHour(
                        lockDetails.assignment?.assignment_data?.valid_to!!.split(
                            "."
                        )[0]
                    )
                    val minute = CommonValues.dateForMin(
                        lockDetails.assignment?.assignment_data?.valid_to!!.split(
                            "."
                        )[0]
                    )


                    var mon = month + 1
                    var m = mon.toString()
                    m = if (mon.toString().length == 1) {
                        "0$mon"
                    } else {
                        mon.toString()
                    }
                    var d = ""
                    d = if (day.toString().length == 1) {
                        "0$day"
                    } else {
                        day.toString()
                    }

                    var isDateSame = false


                    if (value == 2) {
                        Log.e("timeStart ", timeForCalender)

                        val hourStart = CommonValues.dateForHourWithoutTimezone(
                            timeForCalender
                        )
                        val minuteStart = CommonValues.dateForMinWithoutTimeZone(
                            timeForCalender
                        )

                        val dateStart = CommonValues.formatOnlyDateWithoutTimeZone(
                            timeForCalender
                        )
                        val dateEnd = "$d/$m/$year"

                        try {
                            val formatter = SimpleDateFormat("dd/MM/yyyy", Locale("en"))
                            val str1 = dateStart
                            val date1 = formatter.parse(str1)
                            val str2 = dateEnd
                            val date2 = formatter.parse(str2)
                            isDateSame = date1 == date2
                        } catch (e1: ParseException) {
                            e1.printStackTrace()
                        }

                        if (dateValidFrom == ("$year-$m-$d") && dateValidTo != ("$year-$m-$d")) {
                            if (isDateSame) {
                                tpd.setMinTime(hourStart.toInt(), minuteStart.toInt(), 0)
                            } else {
                                tpd.setMinTime(startHour, startMinute, 0)
                            }
                        } else {
                            if (isDateSame) {
                                tpd.setMinTime(hourStart.toInt(), minuteStart.toInt(), 0)
                            }
                        }
                    } else {
                        if (Preferences.userRole.get() == GUEST) {


                            val hourStart = CommonValues.dateForHour(
                                lockDetails.assignment?.assignment_data?.valid_from!!
                            )
                            val minuteStart = CommonValues.dateForMin(
                                lockDetails.assignment?.assignment_data?.valid_from!!
                            )

                            if (dateValidFrom == ("$year-$m-$d") && dateValidTo != ("$year-$m-$d")) {
                                tpd.setMinTime(startHour, startMinute, 0)
                            } else {
                                var dateStart =
                                    startDay.toString() + "/" + (startMonth + 1) + "/" + startYear
                                var dateEnd = "$d/$m/$year"

                                try {
                                    val formatter = SimpleDateFormat("dd/MM/yyyy", Locale("en"))
                                    val str1 = dateStart
                                    val date1 = formatter.parse(str1)
                                    val str2 = dateEnd
                                    val date2 = formatter.parse(str2)
                                    isDateSame = date1 == date2
                                } catch (e1: ParseException) {
                                    e1.printStackTrace()
                                }
                                if (isDateSame) {
                                    val date =
                                        CommonValues.dateFor(
                                            lockDetails.assignment?.assignment_data?.valid_from!!.split(
                                                "+"
                                            )[0]
                                        )
                                    var dateCurrent = CommonValues.dateFor(formattedDateTime)
                                    try {
                                        if (dateCurrent > date) {
                                            tpd.setMinTime(startHour, startMinute, 0)
                                        } else {
                                            tpd.setMinTime(
                                                hourStart.toInt(),
                                                minuteStart.toInt(),
                                                0
                                            )
                                        }
                                    } catch (e1: ParseException) {
                                        e1.printStackTrace()
                                    }

                                }
                            }
                        } else {
                            var dateStart =
                                startDay.toString() + "/" + (startMonth + 1) + "/" + startYear
                            var dateEnd = "$d/$m/$year"

                            try {
                                val formatter = SimpleDateFormat("dd/MM/yyyy", Locale("en"))
                                val str1 = dateStart
                                val date1 = formatter.parse(str1)
                                val str2 = dateEnd
                                val date2 = formatter.parse(str2)
                                isDateSame = date1 == date2
                            } catch (e1: ParseException) {
                                e1.printStackTrace()
                            }

                            if (isDateSame) {
                                tpd.setMinTime(startHour, startMinute, 0)

                            }

                        }
                    }


                    if (dateValidTo == ("$year-$m-$d")) {
                        tpd.setMaxTime(hour.toInt(), minute.toInt(), 0) // MIN: hours, minute, secconds
                    }

                    val selectedDate = LocalDate.of(year, month + 1, day)
                    val startDate = Datetime.fromString(startDateApi)?.toCalendar()
                    if (startDate != null && enforceTTLockTime() && selectedDate.isOneYearOrAfter(startDate)) {
                        if (selectedDate.isAfterOneYear(startDate)) {
                            tpd.setMinTime(startDate.toDate().hour,startDate.toDate().minute, 0)
                        }
                        tpd.setMaxTime(startDate.toDate().hour, startDate.toDate().minute, 0)
                    }

                    tpd.show(supportFragmentManager, "TimePickerDialog")
                },

                startYear,
                startMonth,
                startDay

            )


            if (value == 1) {
                if (Preferences.userRole.get() == GUEST) {
                    if (lockDetails.assignment?.assignment_data?.valid_from.toString()
                            .isNotEmpty()
                    ) {
                        val date =
                            CommonValues.dateFor(
                                lockDetails.assignment?.assignment_data?.valid_from!!.split(
                                    "+"
                                )[0]
                            )


                        var dateCurrent = CommonValues.dateFor(formattedDateTime)

                        try {
                            if (dateCurrent > date) {
                                datePickerDialog.minDate = currentDateTime
                            } else {
                                val dateFormat = SimpleDateFormat("yyyy-MM-dd", Locale("en"))
                                val maxDateCalendar = Calendar.getInstance()
                                maxDateCalendar.timeZone = TimeZone.getTimeZone(Preferences.timeZoneName.get())
                                maxDateCalendar.time = dateFormat.parse(date)!!
                                datePickerDialog.minDate = maxDateCalendar
                            }
                        } catch (e1: ParseException) {
                            e1.printStackTrace()
                        }

//                        if (isDateSame) {
//                            tpd.setMinTime(startHour, startMinute, 0)
//                        }


                    }
                } else {
                    datePickerDialog.minDate = currentDateTime
                }
            } else {
                val date = CommonValues.dateForWithoutTimeZone(timeForCalender)
                val dateFormat = SimpleDateFormat("yyyy-MM-dd", Locale("en"))
                val maxDateCalendar = Calendar.getInstance()
                maxDateCalendar.timeZone = TimeZone.getTimeZone(Preferences.timeZoneName.get())
                maxDateCalendar.time = dateFormat.parse(date)!!
                datePickerDialog.minDate = maxDateCalendar
            }

            if (Preferences.userRole.get() == GUEST) {
                if (lockDetails.assignment?.assignment_data?.valid_to.toString().isNotEmpty()) {
                    val date =
                        CommonValues.dateFor(
                            lockDetails.assignment?.assignment_data?.valid_to!!.split(
                                "+"
                            )[0]
                        )
                    val dateFormat = SimpleDateFormat("yyyy-MM-dd", Locale("en"))
                    val maxDateCalendar = Calendar.getInstance()
                    maxDateCalendar.timeZone = TimeZone.getTimeZone(Preferences.timeZoneName.get())
                    maxDateCalendar.time = dateFormat.parse(date)!!
                    datePickerDialog.maxDate = maxDateCalendar
                }
            } else {
                currentDateTime.add(Calendar.YEAR, 5)
                datePickerDialog.maxDate = currentDateTime

            }

            if (enforceTTLockTime()) {
                datePickerDialog.setDateRangeLimiter(
                    TTLockDateLimiter(
                        initYear = datePickerDialog.minDate.get(Calendar.YEAR),
                        initCalendar = datePickerDialog.minDate,
                    )
                )
            }

            datePickerDialog.show(supportFragmentManager, "Datepickerdialog")
            binding.etEndDate.isEnabled = true
        } catch (e: Exception) {
            e.printStackTrace()
        }

    }

    private fun convertToUtc(date2: Date): String {
        val calendar = Calendar.getInstance()
        calendar.time = date2
        val time = calendar.time
        val finalCalender = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss", Locale("en"))

        val format2 = finalCalender.format(time)
        return "$format2+0000"
    }

    @SuppressLint("SetTextI18n")
    @RequiresApi(Build.VERSION_CODES.O)
    private fun performSearch() {
        hideKeyboard()
        mViewModel.hitSearchUsersApi(
            sharePrefs.token,
            lockDetails.lock.unique_key,
            binding.etEmail.text.toString()
        ).observe(
            this
        ) {
            binding.linearResults.isVisible = true
            if (it.success) {
                binding.txtNotExists.isVisible = false
                userOnlyDetail = it.users[0]
                if (it.users[0].lock_assigned) {
                    binding.alreadyShared.isVisible = true
                    binding.txtDateRange.isVisible = true
                    binding.userDetails.isVisible = true
                    binding.textView12.isVisible = false
                    binding.etRoutine.isVisible = false
                    binding.textView14.isVisible = false
                    binding.etStartDate.isVisible = false
                    binding.textView16.isVisible = false
                    binding.etEndDate.isVisible = false
                    binding.btnSave.isVisible = false
                    if (!it.users[0].detail.valid_from.isNullOrEmpty()) {
                        binding.txtDateRange.isVisible = true
                        binding.txtDateRange.text =
                            CommonValues.formattedDateOnlyEn(
                                it.users[0].assignment_data.valid_from
                            ) + " to " + CommonValues.formattedDateOnlyEn(
                                it.users[0].assignment_data.valid_to
                            )
                    } else {
                        binding.txtDateRange.isVisible = false
                    }
                } else {
                    binding.alreadyShared.isVisible = false
                    binding.userDetails.isVisible = true
                    binding.txtDateRange.isVisible = false
                    binding.etRoutine.isVisible = true
                    binding.textView14.isVisible = true
                    binding.etStartDate.isVisible = true
                    binding.textView16.isVisible = true
                    binding.etEndDate.isVisible = true
                    binding.btnSave.isVisible = true
                    binding.btnSave.text = getString(keyless.data.utils.android.R.string.share_access_and_save)
                    whichApiToHit = "shareAccess"


                    if (Preferences.userRole.get() == GUEST) {
                        binding.textView12.isVisible = false
                        binding.etRoutine.isVisible = false
                    } else {
                        binding.textView12.isVisible = true
                        binding.etRoutine.isVisible = true
                    }
                }


                val name = it.users[0].detail.first_name + " " + it.users[0].detail.last_name
                if (name.isBlank()) {
                    binding.txtName.isVisible = false
                } else {
                    binding.txtName.text = name
                }
                if (!it.users[0].detail.email.isNullOrEmpty()) {
                    binding.txtEmail.isVisible = true
                    binding.txtEmail.text = it.users[0].detail.email
                } else {
                    binding.txtEmail.isVisible = false
                }
                if (!it.users[0].detail.mobile_number.isNullOrEmpty()) {
                    binding.txtPhone.isVisible = false
                    binding.txtPhone.text =
                        it.users[0].detail.country_code + " " + it.users[0].detail.mobile_number
                } else {
                    binding.txtPhone.isVisible = false
                }

                if (checkInValue && lockDetails.lock.primary) {
                    binding.switchCheckIn.isVisible = true
                    binding.txtCheckInReq.isVisible = true
                    binding.infoBtn.isVisible = true
                    binding.switchCheckIn.isChecked = checkInValue
                } else {
                    binding.switchCheckIn.isVisible = false
                    binding.txtCheckInReq.isVisible = false
                    binding.infoBtn.isVisible = false
                }

                binding.switchPasscode.isChecked = passcodeValue
                binding.switchPasscode.isVisible = isSupportPasscode
                binding.txtGeneratePasscode.isVisible = isSupportPasscode
                binding.passcodeInfoBtn.isVisible = isSupportPasscode

            } else {
                if (it.errorType == 1) {
                    binding.alreadyShared.isVisible = false
                    binding.txtDateRange.isVisible = false
                    binding.userDetails.isVisible = false
                    binding.textView12.isVisible = false
                    binding.etRoutine.isVisible = false
                    binding.textView14.isVisible = false
                    binding.etStartDate.isVisible = false
                    binding.textView16.isVisible = false
                    binding.etEndDate.isVisible = false
                    binding.btnSave.isVisible = false
                    binding.txtNotExists.isVisible = true
                    binding.txtNotExists.text = it.message
                } else {
                    binding.txtNotExists.text =
                        resources.getString(
                            keyless.data.utils.android.R.string
                                .this_user_does_not_exist_and_an_invite_email_will_be_sent_to_him_her
                        )
                    binding.alreadyShared.isVisible = false
                    binding.userDetails.isVisible = false
                    binding.txtNotExists.isVisible = true
                    if (Preferences.userRole.get() == GUEST) {
                        binding.textView12.isVisible = false
                        binding.etRoutine.isVisible = false
                    } else {
                        binding.textView12.isVisible = true
                        binding.etRoutine.isVisible = true
                    }
                    binding.textView14.isVisible = true
                    binding.etStartDate.isVisible = true
                    binding.textView16.isVisible = true
                    binding.etEndDate.isVisible = true
                    binding.btnSave.isVisible = true
                    binding.btnSave.text = getString(keyless.data.utils.android.R.string.text_share_access)
                    whichApiToHit = "inviteUser"

                    if (checkInValue && lockDetails.lock.primary) {
                        binding.switchCheckIn.isVisible = true
                        binding.txtCheckInReq.isVisible = true
                        binding.infoBtn.isVisible = true
                        binding.switchCheckIn.isChecked = checkInValue
                    } else {
                        binding.switchCheckIn.isVisible = false
                        binding.txtCheckInReq.isVisible = false
                        binding.infoBtn.isVisible = false
                    }

                    binding.switchPasscode.isChecked = passcodeValue
                    binding.switchPasscode.isVisible = isSupportPasscode
                    binding.txtGeneratePasscode.isVisible = isSupportPasscode
                    binding.passcodeInfoBtn.isVisible = isSupportPasscode
                }

            }

        }


    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (resultCode == Activity.RESULT_OK) {
            val dataMain = data?.getParcelableExtra<RoutineModel>("routine")
            binding.etRoutine.setText(dataMain!!.name)
            routine_id = dataMain._id
            Log.e("routine_id", routine_id)
//            performSearch()
        }
    }

    private fun editAccess(jsonObject: JsonObject) {
        val manager = ttlock as AndroidTTLockManager


        if (!isBluetoothEnabled()) {
            askUserToEnableBluetoothIfNeeded()
            return
        }

        if (detail.assignment_data.passcode.isEmpty() || !lockDetails.ttlockOfflinePasscode()) {
            editAccessApi(jsonObject)
            return
        }

        var job: Job? = null
        val dialog = scanningDialog(onCancel = { job?.cancel() })

        var found = false
        job = lifecycleScope.launch {
            kotlin.runCatching {
                manager.stream.first { it.find { it.name == lockDetails.lock.unique_key } != null }
                found = true
                AndroidTTLock.deletePasscode(
                    passcode = detail.assignment_data.passcode,
                    lockData = lockDetails.lock.access_key
                )
                editAccessApi(jsonObject)

                runOnUiThread { kotlin.runCatching { dialog.dismiss() } }
            }.onFailure {
                if (it is CancellationException) return@launch
                runOnUiThread { kotlin.runCatching { dialog.dismiss() } }
                runOnUiThread { appToast(it.message ?: "Error happened") }
            }
        }

        lifecycleScope.launch {
            delay(20.seconds)
            runOnUiThread { kotlin.runCatching { dialog.dismiss() } }
            if (!found) job.cancel()
        }
    }

    private fun editAccessApi(jsonObject: JsonObject) {
        mViewModel.hitEditAccess(
            sharePrefs.token, jsonObject, idForUserId
        ).observe(this) {
            if (it.success) {
                setResult(Activity.RESULT_OK)
                finish()
            } else {
                defaultDialog(
                    this,
                    it.message,
                    object : OnActionOK {
                        override fun onClickData() {
                        }
                    })
            }

        }
    }

    private fun enforceTTLockTime(): Boolean {
        return lockDetails.ttlockOfflinePasscode() && binding.switchPasscode.isChecked
    }

    private fun LocalDate.isOneYearOrAfter(start: Calendar) = isExactlyOneYear(start) || isAfterOneYear(start)

    private fun LocalDate.isAfterOneYear(start: Calendar): Boolean {
        start.set(Calendar.MILLISECOND, 0)
        start.set(Calendar.SECOND, 0)
        start.set(Calendar.MINUTE, 0)
        val now = start.time.toInstant().atZone(ZoneId.systemDefault()).toLocalDate()
        return this.isAfter(now.plusYears(1).plusDays(1))
    }

    private fun LocalDate.isExactlyOneYear(start: Calendar): Boolean {
        start.set(Calendar.MILLISECOND, 0)
        start.set(Calendar.SECOND, 0)
        start.set(Calendar.MINUTE, 0)
        val now = start.time.toInstant().atZone(ZoneId.systemDefault()).toLocalDate()
        return this.isEqual(now.plusYears(1))
    }
}