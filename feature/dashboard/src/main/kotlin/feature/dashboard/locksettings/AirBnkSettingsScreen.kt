package feature.dashboard.locksettings

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.material3.Divider
import androidx.compose.material3.LocalContentColor
import androidx.compose.material3.Slider
import androidx.compose.material3.Switch
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import core.lock.airbnk.models.AirBnkLockConfig
import core.lock.airbnk.models.AirBnkLockDirection
import feature.common.compose.button.AppButton
import feature.common.compose.button.AppSegmentedButtons
import feature.common.compose.button.SegmentedButtonItem
import feature.common.compose.surface.AppColumn
import feature.common.compose.surface.AppRow
import feature.common.compose.text.AppBodyText
import feature.common.compose.text.AppLabelText
import feature.common.compose.theme.Dimensions
import keyless.feature.dashboard.R

@Composable
internal fun AirBnkSettingsScreen(
    lockModel: String,
    lockConfig: AirBnkLockConfig,
    onSave: (isRightDirection: Boolean, isElectricAssistance: Boolean, isAutoLock: Boolean, autoLockTime: Int) -> Unit
) {
    var isRightDirection by remember(lockConfig.lockDirection) {
        mutableStateOf(lockConfig.lockDirection == AirBnkLockDirection.RIGHT)
    }
    var isElectricalAssistance by remember(lockConfig.autoRotation) { mutableStateOf(lockConfig.autoRotation) }
    var isAutoLockSelected by remember(lockConfig.isAutoLock) { mutableStateOf(lockConfig.isAutoLock) }
    var autoLockTime by remember(lockConfig.autoLockTime) { mutableIntStateOf(lockConfig.autoLockTime) }

    AppColumn(
        arrangement = Arrangement.spacedBy(Dimensions.Paddings.medium)
    ) {
        OptionRow(
            firstWeight = 0.4f,
            first = {
                OptionTitle(title = stringResource(id = R.string.lock_direction))
            },
            secondWeight = 0.6f,
            second = {
                AppSegmentedButtons {
                    SegmentedButtonItem(
                        label = {
                            AppLabelText(text = stringResource(id = R.string.left), color = LocalContentColor.current)
                        },
                        selected = !isRightDirection,
                        onClick = { isRightDirection = false }
                    )
                    SegmentedButtonItem(
                        label = {
                            AppLabelText(text = stringResource(id = R.string.right), color = LocalContentColor.current)
                        },
                        selected = isRightDirection,
                        onClick = { isRightDirection = true }
                    )
                }
            }
        )

        Divider()

        OptionRow(
            firstWeight = 0.7f,
            first = {
                OptionTitle(title = stringResource(id = R.string.electric_assistance))
            },
            secondWeight = 0.3f,
            second = {
                Switch(
                    checked = isElectricalAssistance,
                    onCheckedChange = { isElectricalAssistance = it }
                )
            }
        )

        Divider()

        OptionRow(
            firstWeight = 0.7f,
            first = {
                OptionTitle(title = stringResource(id = R.string.auto_lock))
            },
            secondWeight = 0.3f,
            second = {
                Switch(
                    checked = isAutoLockSelected,
                    onCheckedChange = { isAutoLockSelected = it }
                )
            }
        )

        if (isAutoLockSelected) {
            OptionRow(
                firstWeight = 0.7f,
                first = {
                    OptionTitle(title = stringResource(id = R.string.auto_lock_time))
                },
                secondWeight = 0.3f,
                second = {
                    OptionTitle(title = stringResource(id = R.string.seconds_templete, "$autoLockTime"))
                }
            )

            Slider(
                value = autoLockTime.toFloat(),
                onValueChange = { autoLockTime = it.toInt() },
                steps = 4,
                valueRange = 10f..60f
            )
        }

        AppButton(
            modifier = Modifier.fillMaxWidth(),
            text = stringResource(id = R.string.save),
            enabled = true,
            onClick = {
                onSave(isRightDirection, isElectricalAssistance, isAutoLockSelected, autoLockTime)
            }
        )
    }
}

@Composable
private fun OptionRow(
    firstWeight: Float = 0.5f,
    secondWeight: Float = 0.5f,
    first: @Composable () -> Unit,
    second: @Composable () -> Unit,
    modifier: Modifier = Modifier
) {
    AppRow(
        modifier = modifier.fillMaxWidth(),
        alignment = Alignment.CenterVertically,
        arrangement = Arrangement.SpaceBetween
    ) {
        Box(modifier = Modifier.weight(firstWeight)) {
            first()
        }

        Box(modifier = Modifier.weight(0.025f))

        Box(modifier = Modifier.weight(secondWeight), contentAlignment = Alignment.CenterEnd) {
            second()
        }
    }
}

@Composable
private fun OptionTitle(
    title: String
) {
    AppBodyText(text = title)
}