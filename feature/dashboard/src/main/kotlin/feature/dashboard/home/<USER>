package feature.dashboard.home

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.remember
import androidx.compose.ui.platform.ComposeView
import androidx.fragment.app.Fragment
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import core.common.status.StatusRepository
import data.keyless.home.LockSummary
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.launch
import org.koin.android.ext.android.inject
import org.koin.androidx.viewmodel.ext.android.viewModel
import presentation.common.feature.AppTheme
import presentation.home.dashboard.domain.ScreenData
import presentation.home.dashboard.domain.SideEffect
import presentation.home.dashboard.feature.DashboardScreen
import presentation.home.dashboard.feature.StateHolder
import presentation.home.dashboard.feature.UIState
import presentation.common.feature.state.DerivedState

class DashboardHomeFragment : Fragment() {

    private val viewModel: DashboardHomeAndroidViewModel by viewModel()
    private val status: StatusRepository by inject()

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View {
        return ComposeView(requireContext()).apply { setContent { AppTheme { Screen() } } }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        viewLifecycleOwner.lifecycleScope.launch {
            repeatOnLifecycle(Lifecycle.State.STARTED) {
                viewModel.sideEffects.collect(::onSideEffect)
            }
        }
    }

    private fun onSideEffect(sideEffect: SideEffect) = when (sideEffect) {
        is SideEffect.NavToChangePassword -> {
            // Handle navigation to change password
        }
        is SideEffect.NavToLockDetails -> {
            // Handle navigation to lock details
            navigateToLockDetails(sideEffect.lockSummary)
        }
    }

    private fun navigateToLockDetails(lockSummary: LockSummary) {
        // TODO: Implement navigation to lock details screen
        // This should match the existing navigation pattern in the app
    }

    @Composable
    private fun Screen() {
        val screenData = viewModel.screenData.collectAsState(ScreenData.empty)
        val state = remember(screenData.value) {
            val data = DerivedState(screenData.value, flowOf(screenData.value))
            val uiState = UIState(data = data)
            StateHolder(data = data, ui = uiState)
        }

        DashboardScreen(
            state = state,
            onEvent = viewModel::onEvent
        )
    }
}
