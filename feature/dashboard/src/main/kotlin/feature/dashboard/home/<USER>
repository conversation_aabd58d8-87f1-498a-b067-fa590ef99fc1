package feature.dashboard.home

import androidx.compose.runtime.State
import androidx.lifecycle.viewModelScope
import core.common.coroutines.AbstractCoroutineDispatcher
import domain.common.ErrorHandler
import kotlinx.coroutines.launch
import presentation.home.dashboard.domain.ViewModel
import presentation.home.dashboard.domain.Event
import presentation.home.dashboard.domain.ScreenData
import presentation.home.dashboard.feature.StateHolder
import presentation.home.dashboard.feature.UIState

internal class DashboardHomeAndroidViewModel(
    private val domain: ViewModel,
    private val dispatchers: AbstractCoroutineDispatcher,
    private val errorHandler: <PERSON>rrorHandler,
    private val onClear: () -> Unit
) : androidx.lifecycle.ViewModel() {

    private var stateHolder: StateHolder? = null

    val screenData = domain.screenDataStream
    val sideEffects = domain.sideEffectsStream

    fun initState(data: State<ScreenData>, state: UIState): StateHolder {
        if (stateHolder == null) stateHolder = StateHolder(data = data, ui = state)
        if (data != stateHolder?.data) stateHolder = stateHolder!!.copy(data = data)

        return stateHolder!!
    }

    fun onEvent(event: Event) {
        viewModelScope.launch(dispatchers.default) { errorHandler.async { domain.onEvent(event) } }
    }

    override fun onCleared() {
        super.onCleared()
        onClear()
    }
}
