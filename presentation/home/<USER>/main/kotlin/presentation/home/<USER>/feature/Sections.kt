package presentation.home.dashboard.feature

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyListScope
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import keyless.presentation.common.R
import presentation.common.feature.components.AppCarousel
import presentation.common.feature.components.AppColumn
import presentation.common.feature.components.AppFlowRow
import presentation.common.feature.components.AppRow
import presentation.common.feature.components.AppScrollRow
import presentation.common.feature.components.AppSurface
import presentation.common.feature.components.DesignSystem
import presentation.common.feature.components.NoLocksIllustration
import presentation.common.feature.components.hiddenClickable

@Composable
internal fun ChangePasswordRow(
    modifier: Modifier = Modifier,
    state: ChangePasswordState.Show,
    onChangePassword: () -> Unit
) {
    AppSurface(
        modifier = modifier.hiddenClickable(onClick = onChangePassword),
        paddings = PaddingValues(horizontal = DesignSystem.Padding.large, vertical = DesignSystem.Padding.medium),
        color = MaterialTheme.colorScheme.errorContainer
    ) {
        ChangePasswordText(state)
    }
}

@Composable
internal fun YourKeysRow(modifier: Modifier = Modifier, onClaimKey: () -> Unit) {
    AppRow(
        modifier = modifier.fillMaxWidth(),
        arrangement = Arrangement.SpaceBetween,
        alignment = Alignment.CenterVertically
    ) {
        YourKeysText()

        ClaimYourKeyButton(onClaimKey)
    }
}

@Composable
internal fun BoxScope.HomeTopBar(state: StateHolder) {
    GuestTopBar(state)
}

@Composable
internal fun BoxScope.GuestTopBar(state: StateHolder) {
    AppRow(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = DesignSystem.Padding.large, vertical = DesignSystem.Padding.medium),
        arrangement = Arrangement.spacedBy(DesignSystem.Padding.small),
        alignment = Alignment.CenterVertically
    ) {
        GuestTopBarContent(state)
    }
}

@Composable
internal fun GuestCarousel(modifier: Modifier = Modifier) {
    AppCarousel(
        modifier = modifier,
        count = 3,
        contentPadding = PaddingValues(0.dp),
        gridSpacing = DesignSystem.Padding.screen
    ) { index ->
        GuestCarouselItems(index)
    }
}

@Composable
private fun GuestCarouselItems(i: Int) {
    when (i) {
        0 -> GuestCarouselItem(index = i, titleRes = R.string.welcome_to_keyless, textRes = R.string.one_application)
        1 -> GuestCarouselItem(index = i, titleRes = R.string.share_access, textRes = R.string.keyless_enables)
        2 -> GuestCarouselItem(index = i, titleRes = R.string.quick_services, textRes = R.string.choose_from_a)
    }
}


@Composable
internal fun LocksSection(
    modifier: Modifier = Modifier,
    state: StateHolder,
    onLockClick: (data.keyless.home.LockSummary) -> Unit = {}
) {
    if (state.ui.locks.value.isEmpty()) {
        NoLocksIllustration(modifier = modifier.fillMaxWidth())
    } else {
        LocksSectionLocks(modifier = modifier, state = state, onLockClick = onLockClick)
    }
}

@Composable
internal fun LocksSectionLocks(
    modifier: Modifier = Modifier,
    state: StateHolder,
    onLockClick: (data.keyless.home.LockSummary) -> Unit = {}
) {
    AppFlowRow(
        modifier = modifier,
        maxItemsInEachRow = 2,
        arrangement = Arrangement.spacedBy(DesignSystem.Padding.screen),
        alignment = Arrangement.spacedBy(DesignSystem.Padding.screen)
    ) {
        for (summary in state.ui.locks.value) LockItem(
            modifier = Modifier.weight(1f),
            lock = summary,
            onLockClick = onLockClick
        )

        if (state.ui.locks.value.size % 2 != 0) Box(modifier = Modifier.weight(1f))
    }
}

@Composable
internal fun QuickServicesSection(modifier: Modifier = Modifier) {
    AppColumn(
        modifier = modifier.fillMaxWidth(),
        arrangement = Arrangement.spacedBy(DesignSystem.Padding.medium)
    ) {
        QuickServicesText()

        QuickServicesCarousel()
    }
}

@Composable
private fun QuickServicesCarousel() {
    AppScrollRow(
        modifier = Modifier.fillMaxWidth(),
        contentPadding = PaddingValues(horizontal = DesignSystem.Padding.screen),
        arrangement = Arrangement.spacedBy(DesignSystem.Padding.medium)
    ) {
        QuickServicesCarouselItems()
    }
}

private fun LazyListScope.QuickServicesCarouselItems() {
    items(count = 7) {
        when (it) {
            0 -> QuickServiceItem(imageRes = R.mipmap.taxi_image, textRes = R.string.taxi)
            1 -> QuickServiceItem(imageRes = R.mipmap.delivery_image, textRes = R.string.food_delivery)
            2 -> QuickServiceItem(imageRes = R.mipmap.house_keeping_image, textRes = R.string.house_keeping)
            3 -> QuickServiceItem(imageRes = R.mipmap.ambulance_image, textRes = R.string.emergency_services)
            4 -> QuickServiceItem(imageRes = R.mipmap.local_experinces, textRes = R.string.local_experiences)
            5 -> QuickServiceItem(imageRes = R.mipmap.groceries_image, textRes = R.string.groceries)
            6 -> QuickServiceItem(imageRes = R.mipmap.health_care, textRes = R.string.home_health_care)
        }
    }
}