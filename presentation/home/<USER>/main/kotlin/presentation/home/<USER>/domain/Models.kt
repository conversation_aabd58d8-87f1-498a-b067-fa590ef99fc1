package presentation.home.dashboard.domain

import core.common.status.Status
import data.keyless.users.models.CheckUserRequest
import data.keyless.users.models.CheckUserResponse

data class ScreenData(
    val userCheck: CheckUserResponse?,
    val status: List<Status>
) {
    companion object {
        val empty = ScreenData(userCheck = null, status = emptyList())
    }
}

sealed interface SideEffect {
    object NavToChangePassword : SideEffect
    data class NavToLockDetails(val lockSummary: data.keyless.home.LockSummary) : SideEffect
}

sealed interface Event

sealed interface UserAction : Event {
    data class OnLockClick(val lockSummary: data.keyless.home.LockSummary) : UserAction
}

sealed interface ScreenEvent: Event {
    class Init(val checkUserRequest: CheckUserRequest) : ScreenEvent
}