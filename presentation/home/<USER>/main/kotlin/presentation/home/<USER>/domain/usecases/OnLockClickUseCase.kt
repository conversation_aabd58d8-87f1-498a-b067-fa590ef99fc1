package presentation.home.dashboard.domain.usecases

import core.common.error.KError
import core.common.message.Message
import core.common.status.StatusRepository
import core.monitoring.common.repository.Logger
import core.monitoring.common.repository.async
import data.common.isAfter
import data.common.lastServerTime
import data.common.now
import data.keyless.home.LockSummary
import keyless.presentation.common.R
import presentation.common.domain.repositories.SideEffectsRepository
import presentation.home.dashboard.domain.SideEffect
import presentation.home.dashboard.domain.UserAction
import kotlinx.datetime.toLocalDateTime
import kotlinx.datetime.TimeZone

internal class OnLockClickUseCase(
    private val logger: Logger,
    private val status: StatusRepository,
    private val sideEffects: SideEffectsRepository<SideEffect>
) {

    suspend fun execute(event: UserAction.OnLockClick) = logger.async {
        val lockSummary = event.lockSummary
        
        if (lockSummary.isLockActiveForUser) {
            // Lock is active, navigate to lock details
            sideEffects.emit(SideEffect.NavToLockDetails(lockSummary))
        } else {
            // Lock is not active, show accessibility information
            handleInactiveLock(lockSummary)
        }
    }

    private suspend fun handleInactiveLock(lockSummary: LockSummary) = logger.async {
        val validFrom = lockSummary.assignment?.assignmentData?.validFromDateTime

        if (validFrom != null) {
            val serverTime = lastServerTime()
            val currentTime = serverTime ?: now()

            if (validFrom.isAfter(currentTime)) {
                // Lock will be accessible in the future
                val accessibleDate = formatAccessibleDate(validFrom)
                sideEffects.emit(SideEffect.ShowLockAccessibilityInfo(accessibleDate))
            } else {
                // Lock access has expired or other issue
                status.info(Message(R.string.lock_access_not_available))
            }
        } else {
            // No assignment data available
            status.info(Message(R.string.lock_access_not_available))
        }
    }

    private fun formatAccessibleDate(instant: kotlinx.datetime.Instant): String {
        val localDateTime = instant.toLocalDateTime(TimeZone.currentSystemDefault())
        return "${localDateTime.date} ${localDateTime.time}"
    }
}
