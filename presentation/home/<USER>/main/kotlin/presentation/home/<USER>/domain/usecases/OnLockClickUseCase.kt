package presentation.home.dashboard.domain.usecases

import core.common.error.KError
import core.common.message.Message
import core.common.status.StatusRepository
import core.monitoring.common.repository.Logger
import core.monitoring.common.repository.async
import data.common.preferences.Preferences
import data.common.preferences.Roles.CUSTOMER_SERVICES
import data.common.preferences.Roles.SYSTEM_MANAGER
import data.common.preferences.Roles.VIEWER_ACCESS
import data.common.utils.CommonValues
import data.keyless.home.LockSummary
import keyless.feature.common.R
import presentation.common.domain.repositories.SideEffectsRepository
import presentation.home.dashboard.domain.SideEffect
import presentation.home.dashboard.domain.UserAction
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

internal class OnLockClickUseCase(
    private val logger: Logger,
    private val status: StatusRepository,
    private val sideEffects: SideEffectsRepository<SideEffect>
) {

    suspend fun execute(event: UserAction.OnLockClick) = logger.async {
        val lockSummary = event.lockSummary

        // Check if user has role-based restrictions (same logic as original)
        if (Preferences.userRole.get() == CommonValues.GUEST ||
            Preferences.role.get() == SYSTEM_MANAGER ||
            Preferences.role.get() == VIEWER_ACCESS ||
            Preferences.role.get() == CUSTOMER_SERVICES
        ) {
            handleRestrictedUserAccess(lockSummary)
        } else {
            // No restrictions, navigate directly to lock details
            sideEffects.emit(SideEffect.NavToLockDetails(lockSummary))
        }
    }

    private suspend fun handleRestrictedUserAccess(lockSummary: LockSummary) = logger.async {
        val assignment = lockSummary.assignment?.assignmentData
        if (assignment?.validFrom == null) {
            status.fail(KError.Info(Message(R.string.this_lock_will_be_accessible_on)))
            return@async
        }

        val startDate = CommonValues.formattedDateOnlyEn(
            assignment.validFrom.split("+")[0]
        )

        val dateToDisplay = CommonValues.formatDdMmYyyyy(
            assignment.validFrom.split("+")[0]
        )

        val currentDate = if (CommonValues.isNetworkAvailable()) {
            if (!CommonValues.serverDateTime.isNullOrEmpty()) {
                CommonValues.formattedDateOnlyEn(
                    CommonValues.serverDateTime.split(".")[0]
                )
            } else {
                val sdf = SimpleDateFormat(CommonValues.DATE_FORMAT, Locale("en"))
                sdf.format(Date())
            }
        } else {
            val sdf = SimpleDateFormat(CommonValues.DATE_FORMAT, Locale("en"))
            sdf.format(Date())
        }

        val sdf1 = SimpleDateFormat(CommonValues.DATE_FORMAT, Locale("en"))
        val startDateTimeMain: Date = sdf1.parse(startDate)!!
        val currentDateTimeMain: Date = sdf1.parse(currentDate)!!

        if (currentDateTimeMain.after(startDateTimeMain) || currentDateTimeMain == startDateTimeMain) {
            // Access allowed, navigate to lock details
            sideEffects.emit(SideEffect.NavToLockDetails(lockSummary))
        } else {
            // Access not allowed, show error with date
            val errorMessage = "${getString(R.string.this_lock_will_be_accessible_on)} \n$dateToDisplay"
            status.fail(KError.Info(Message.fromString(errorMessage)))
        }
    }

    private fun getString(resId: Int): String {
        // This will be handled by the Status system which has access to context
        return ""
    }
}
