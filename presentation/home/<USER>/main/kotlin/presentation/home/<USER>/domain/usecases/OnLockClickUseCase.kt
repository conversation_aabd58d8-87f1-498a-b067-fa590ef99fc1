package presentation.home.dashboard.domain.usecases

import android.content.Context
import core.common.error.KError
import core.common.message.Message
import core.common.status.StatusRepository
import core.monitoring.common.repository.Logger
import core.monitoring.common.repository.async
import data.common.preferences.Preferences
import data.common.preferences.Roles.CUSTOMER_SERVICES
import data.common.preferences.Roles.SYSTEM_MANAGER
import data.common.preferences.Roles.VIEWER_ACCESS
import data.common.utils.CommonValues
import data.keyless.home.LockSummary
import keyless.presentation.common.R
import presentation.common.domain.repositories.SideEffectsRepository
import presentation.home.dashboard.domain.SideEffect
import presentation.home.dashboard.domain.UserAction
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

internal class OnLockClickUseCase(
    private val logger: Logger,
    private val status: StatusRepository,
    private val sideEffects: SideEffectsRepository<SideEffect>
) {

    suspend fun execute(event: UserAction.OnLockClick) = logger.async {
        val lockSummary = event.lockSummary

        if (lockSummary.isLockActiveForUser) {
            // Lock is active, navigate to lock details
            sideEffects.emit(SideEffect.NavToLockDetails(lockSummary))
        } else {
            // Lock is not active, show error message
            status.fail(KError.Info(Message(R.string.this_lock_will_be_accessible_on)))
        }
    }
}
