plugins {
    id("keyless.kotlin")
}

dependencies {
    implementation(project(":core-common"))
    implementation(project(":core-http-client-common"))
    implementation(project(":core-locks-manager"))
    implementation(project(":core-monitoring-common"))

    implementation(project(":data-common"))
    api(project(":data-lock-common"))
    implementation(project(":data-database"))
}

dependencies {
    testImplementation(project(":data-test"))
}