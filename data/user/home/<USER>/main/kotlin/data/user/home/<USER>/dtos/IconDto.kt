package data.user.home.models.dtos

import data.lock.common.lock.models.lock.Icon
import kotlinx.serialization.Serializable

@Serializable
internal data class IconDto(
    val _id: String = "",
    val name: String = "",
    val icon: String = "",
    val type: String = "",
    val createdAt: String = ""
) {
    fun toModel(): Icon {
        return Icon(
            name = name,
            icon = icon,
            type = type,
            createdAt = createdAt,
            id = _id
        )
    }
}