package data.user.guest.models.dtos

import data.user.guest.models.GuestAssignment
import kotlinx.serialization.Serializable

@Serializable
data class GuestAssignmentResponseDto(
    val assignments: List<GuestAssignmentDto>
)

@Serializable
data class GuestAssignmentDto(
    val lock_name: String,
    val valid_from: String,
    val valid_to: String,
    val booking_number: String,
    val first_name: String,
    val last_name: String,
    val fullName: String = ""
) {
    fun toModel(): GuestAssignment {
        return GuestAssignment(
            firstName = first_name,
            lastName = last_name,
            bookingNumber = booking_number,
            lockName = lock_name,
            validFrom = valid_from,
            validTo = valid_to,
            fullName = fullName
        )
    }
}