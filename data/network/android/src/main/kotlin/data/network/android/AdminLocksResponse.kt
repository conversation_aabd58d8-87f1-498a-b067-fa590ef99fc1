package data.network.android

import androidx.annotation.Keep
import core.locks.logs.models.LockLogActionType
import data.common.preferences.Preferences
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

@Keep
class AdminLocksResponse {
    var locks: ArrayList<LockModelAdmin> = ArrayList()
    var success: Boolean = false
    var total_locks: Int = 0
}

@Keep
class LockModelAdmin {
    var alloted_to: AllotedToModel = AllotedToModel()
    var lock: Lock = Lock()
}

@Keep
class AllotedToModel {
    var id: String = ""
    var name: String = ""
}

@Keep
class Lock {
    var __v: Int = 0
    var _id: String = ""
    var access_key: String = ""
    var encrypted_key: String = ""
    var alloted: Boolean = false
    var battery_level: Int = 0
    var createdAt: String = ""
    var desc: String = ""
    var image: String = ""
    var lock_uid: String = ""
    var name: String = ""
    var provider: String = ""
    var status: Int = 0
    var inventory_status: Int = 0
    var unique_key: String = ""
    var internal_id: String = ""
}

fun Lock.log(
    actionType: LockLogActionType,
    message: String,
    data: String
) {
    CoroutineScope(Dispatchers.IO).launch {
        Preferences.lockLogger.log(
            actionType = actionType.toString(),
            lockInternalId = internal_id,
            message = message,
            lockInfoData = data,
            provider = provider,
            user = Preferences.userFullName.get()
        )
    }
}