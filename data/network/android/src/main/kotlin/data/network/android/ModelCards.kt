package data.network.android

import android.os.Parcel
import android.os.Parcelable
import androidx.annotation.Keep

@Keep
class ModelCards() : Parcelable {
    var success: Boolean = false
    var total_count: Int = 0
    var data: ArrayList<DataModelCard> = ArrayList()

    constructor(parcel: Parcel) : this() {
        success = parcel.readByte() != 0.toByte()
        total_count = parcel.readInt()
        data = parcel.createTypedArrayList(DataModelCard)!!
    }

    override fun writeToParcel(parcel: Parcel, flags: Int) {
        parcel.writeByte(if (success) 1 else 0)
        parcel.writeInt(0)
        parcel.writeTypedList(data)
    }

    override fun describeContents(): Int {
        return 0
    }

    companion object CREATOR : Parcelable.Creator<ModelCards> {
        override fun createFromParcel(parcel: Parcel): ModelCards {
            return ModelCards(parcel)
        }

        override fun newArray(size: Int): Array<ModelCards?> {
            return arrayOfNulls(size)
        }
    }
}

@Keep
class DataModelCard() : Parcelable {

    var _id: String = ""
    var card_uid: String = ""
    var internal_id: String = ""
    var created_at: String = ""
    var updated_at: String = ""
    var type: Int = 0
    var admin_id: String = ""
    var status: Int = 0
    var __v: Int = 0
    var company_assign: CompanyAssignModel = CompanyAssignModel()
    var lock_assign: ArrayList<LockModelManage> = ArrayList()

    constructor(parcel: Parcel) : this() {
        _id = parcel.readString()!!
        card_uid = parcel.readString()!!
        internal_id = parcel.readString()!!
        created_at = parcel.readString()!!
        updated_at = parcel.readString()!!
        type = parcel.readInt()
        admin_id = parcel.readString()!!
        status = parcel.readInt()
        __v = parcel.readInt()
        company_assign = parcel.readParcelable(CompanyAssignModel::class.java.classLoader)!!
    }

    override fun writeToParcel(parcel: Parcel, flags: Int) {
        parcel.writeString(_id)
        parcel.writeString(card_uid)
        parcel.writeString(internal_id)
        parcel.writeString(created_at)
        parcel.writeString(updated_at)
        parcel.writeInt(type)
        parcel.writeString(admin_id)
        parcel.writeInt(status)
        parcel.writeInt(__v)
        parcel.writeParcelable(company_assign, flags)
    }

    override fun describeContents(): Int {
        return 0
    }

    companion object CREATOR : Parcelable.Creator<DataModelCard> {
        override fun createFromParcel(parcel: Parcel): DataModelCard {
            return DataModelCard(parcel)
        }

        override fun newArray(size: Int): Array<DataModelCard?> {
            return arrayOfNulls(size)
        }
    }
}

@Keep
class CompanyAssignModel() : Parcelable {

    var _id: String = ""
    var card_id: String = ""
    var company_id: String = ""
    var admin_id: String = ""
    var created_at: String = ""
    var updated_at: String = ""
    var status: Int = 0
    var __v: Int = 0

    constructor(parcel: Parcel) : this() {
        _id = parcel.readString()!!
        card_id = parcel.readString()!!
        company_id = parcel.readString()!!
        admin_id = parcel.readString()!!
        created_at = parcel.readString()!!
        updated_at = parcel.readString()!!
        status = parcel.readInt()
        __v = parcel.readInt()
    }

    override fun writeToParcel(parcel: Parcel, flags: Int) {
        parcel.writeString(_id)
        parcel.writeString(card_id)
        parcel.writeString(company_id)
        parcel.writeString(admin_id)
        parcel.writeString(created_at)
        parcel.writeString(updated_at)
        parcel.writeInt(status)
        parcel.writeInt(__v)
    }

    override fun describeContents(): Int {
        return 0
    }

    companion object CREATOR : Parcelable.Creator<CompanyAssignModel> {
        override fun createFromParcel(parcel: Parcel): CompanyAssignModel {
            return CompanyAssignModel(parcel)
        }

        override fun newArray(size: Int): Array<CompanyAssignModel?> {
            return arrayOfNulls(size)
        }
    }
}