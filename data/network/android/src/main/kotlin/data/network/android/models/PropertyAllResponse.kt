package data.network.android.models

import androidx.annotation.Keep
import java.util.ArrayList

@Keep
data class PropertyAllResponse(
    var lock_count: Int?,
    var properties: ArrayList<Property>?,
    var success: Boolean?
) {
    @Keep
    data class Property(
        var __v: Int?,
        var _id: String?,
        var area: String?,
        var building_name: String?,
        var created_at: String?,
        var emirate: String?,
        var latitude: String?,
        var longitude: String?,
        var manager_id: String?,
        var total_floors: Long?
    )
}