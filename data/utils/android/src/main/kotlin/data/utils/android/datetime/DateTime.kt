package data.utils.android.datetime

import android.annotation.SuppressLint
import android.content.Context
import data.common.preferences.Preferences
import data.utils.android.CommonValues.Companion.DATE_FORMAT
import data.utils.android.settings.SharedPreferenceUtils
import java.text.DateFormat
import java.text.SimpleDateFormat
import java.time.Instant
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.ZoneId
import java.time.ZonedDateTime
import java.util.Calendar
import java.util.Date
import java.util.Locale
import java.util.TimeZone


internal object DateTime {
    fun formattedDateOnlyEn(validFrom: String): String {
        return try {
            val parser = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss", Locale("en"))
            parser.timeZone = TimeZone.getTimeZone("UTC")
            val value: Date = parser.parse(validFrom)
            val outputFormatter1: DateFormat = SimpleDateFormat(DATE_FORMAT, Locale("en"))
            outputFormatter1.timeZone = TimeZone.getTimeZone(Preferences.timeZoneName.get())
            outputFormatter1.format(value)
        } catch (e: Exception) {
            ""
        }
    }


    fun formatDdMmYyyyy(validFrom: String, context: Context): String {
        val parser = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss")
        parser.timeZone = TimeZone.getTimeZone("UTC")
        val value: Date = parser.parse(validFrom)
        val outputFormatter1: DateFormat = SimpleDateFormat(
            "dd-MM-yyyy hh:mm a",
            Locale(SharedPreferenceUtils.getInstance(context).language!!)
        )
        outputFormatter1.timeZone = TimeZone.getTimeZone(Preferences.timeZoneName.get())

        return outputFormatter1.format(value)
    }


    fun formatTimeDate(validFrom: String, context: Context): String {
        return try {
            val parser = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss", Locale("en"))
            parser.timeZone = TimeZone.getTimeZone("UTC")
            val value: Date = parser.parse(validFrom)
            val outputFormatter1: DateFormat =
                SimpleDateFormat(DATE_FORMAT, Locale(SharedPreferenceUtils.getInstance(context).language!!))
            outputFormatter1.timeZone = TimeZone.getTimeZone(Preferences.timeZoneName.get())
            outputFormatter1.format(value)
        } catch (e: Exception) {
            ""
        }
    }

    fun formatTimeDateSimple(validFrom: String): String {
        return try {
            val parser = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss")
            parser.timeZone = TimeZone.getTimeZone("UTC")
            val value: Date = parser.parse(validFrom)
            val outputFormatter1: DateFormat = SimpleDateFormat(DATE_FORMAT)
            outputFormatter1.timeZone = TimeZone.getTimeZone(Preferences.timeZoneName.get())
            outputFormatter1.format(value)
        } catch (e: Exception) {
            ""
        }
    }


    fun formatOnlyDate(validFrom: String): String {
        val parser = SimpleDateFormat("yyyy-MM-dd", Locale("en"))
        parser.timeZone = TimeZone.getTimeZone("UTC")
        val value: Date = parser.parse(validFrom)
        val outputFormatter1: DateFormat = SimpleDateFormat("dd/MM/yyyy", Locale("en"))
        outputFormatter1.timeZone = TimeZone.getTimeZone(Preferences.timeZoneName.get())
        return outputFormatter1.format(value)
    }


    fun formatOnlyDateWithoutTimeZone(validFrom: String): String {
        val parser = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss", Locale("en"))
//            parser.timeZone = TimeZone.getTimeZone("UTC")
        val value: Date = parser.parse(validFrom)
        val outputFormatter1: DateFormat = SimpleDateFormat("dd/MM/yyyy", Locale("en"))
        outputFormatter1.timeZone = TimeZone.getTimeZone(Preferences.timeZoneName.get())
        return outputFormatter1.format(value)

    }


    fun formatCompare(validFrom: String): String {
        val parser = SimpleDateFormat("dd-MMM-yyyy HH:mm", Locale("en"))
        parser.timeZone = TimeZone.getTimeZone("UTC")
        val value: Date = parser.parse(validFrom)
        val outputFormatter1: DateFormat =
            SimpleDateFormat("dd-MM-yyyy HH:mm", Locale("en"))
        outputFormatter1.timeZone = TimeZone.getTimeZone(Preferences.timeZoneName.get())

        return outputFormatter1.format(value)
    }


    fun formatTimeDate1(validFrom: String, context: Context): String {
        val parser = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss")
        parser.timeZone = TimeZone.getTimeZone("UTC")
        val value: Date = parser.parse(validFrom)
        val outputFormatter1: DateFormat =
            SimpleDateFormat("dd MMM, yy", Locale(SharedPreferenceUtils.getInstance(context).language!!))
        outputFormatter1.timeZone = TimeZone.getTimeZone(Preferences.timeZoneName.get())
        return outputFormatter1.format(value)
    }


    fun formatDateEdit(validFrom: String): String {
        val parser = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ssZ", Locale("en"))
//            parser.timeZone = TimeZone.getTimeZone(Preferences.timeZoneName.get())
        val value: Date = parser.parse(validFrom)
        val outputFormatter1: DateFormat = SimpleDateFormat("dd-MMM-yyyy HH:mm", Locale("en"))
        outputFormatter1.timeZone = TimeZone.getTimeZone(Preferences.timeZoneName.get())
        return outputFormatter1.format(value)
    }


    @SuppressLint("SimpleDateFormat")
    fun dateForLockHistory(): String {
        val currentDate = Date()
        val sdf = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSZ", Locale("en"))
        sdf.timeZone = TimeZone.getTimeZone(Preferences.timeZoneOffset.get())
        return sdf.format(currentDate)
    }


    fun dateFor(dateMain: String): String {
        val df: DateFormat = SimpleDateFormat("yyyy-MM-dd'T'hh:mm:ss", Locale("en"))
        df.timeZone = TimeZone.getTimeZone("UTC")
        val value: Date = df.parse(dateMain)
        val outputFormatter1: DateFormat = SimpleDateFormat("yyyy-MM-dd", Locale("en"))
        outputFormatter1.timeZone = TimeZone.getTimeZone(Preferences.timeZoneName.get())
        return outputFormatter1.format(value)
    }

    fun dateForWithoutTimeZone(dateMain: String): String {
        val df: DateFormat = SimpleDateFormat("yyyy-MM-dd'T'hh:mm:ss", Locale("en"))
        val value: Date = df.parse(dateMain)
        val outputFormatter1: DateFormat = SimpleDateFormat("yyyy-MM-dd", Locale("en"))
        outputFormatter1.timeZone = TimeZone.getTimeZone(Preferences.timeZoneName.get())
        return outputFormatter1.format(value)
    }


    fun dateForHour(dateMain: String): String {
        val parser = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss", Locale("en"))
        parser.timeZone = TimeZone.getTimeZone("UTC")
        val value: Date = parser.parse(dateMain)
        val outputFormatter1: DateFormat = SimpleDateFormat("HH", Locale("en"))
        outputFormatter1.timeZone = TimeZone.getTimeZone(Preferences.timeZoneName.get())
        return outputFormatter1.format(value)
    }

    fun dateForHourWithoutTimezone(dateMain: String): String {
        val parser = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss", Locale("en"))
        val value: Date = parser.parse(dateMain)
        val outputFormatter1: DateFormat = SimpleDateFormat("HH", Locale("en"))
        outputFormatter1.timeZone = TimeZone.getTimeZone(Preferences.timeZoneName.get())
        return outputFormatter1.format(value)
    }


    fun dateForMin(dateMain: String): String {
        val parser = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss", Locale("en"))
        parser.timeZone = TimeZone.getTimeZone("UTC")
        val value: Date = parser.parse(dateMain)
        val outputFormatter1: DateFormat = SimpleDateFormat("mm", Locale("en"))
        outputFormatter1.timeZone = TimeZone.getTimeZone(Preferences.timeZoneName.get())
        return outputFormatter1.format(value)
    }


    fun dateForMinWithoutTimeZone(dateMain: String): String {
        val parser = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss", Locale("en"))
        val value: Date = parser.parse(dateMain)
        val outputFormatter1: DateFormat = SimpleDateFormat("mm", Locale("en"))
        outputFormatter1.timeZone = TimeZone.getTimeZone(Preferences.timeZoneName.get())

        return outputFormatter1.format(value)
    }


    fun dateZformat(dateMain: String, context: Context): String {
        val df: DateFormat = SimpleDateFormat("yyyy-MM-dd'T'hh:mm:ss.SSS'Z'", Locale("en"))
        df.timeZone = TimeZone.getTimeZone("UTC")
        val value: Date = df.parse(dateMain)
        val outputFormatter1: DateFormat =
            SimpleDateFormat("dd-MMM-yyyy hh:mm a", Locale(SharedPreferenceUtils.getInstance(context).language!!))
        outputFormatter1.timeZone = TimeZone.getTimeZone(Preferences.timeZoneName.get())
        return outputFormatter1.format(value)
    }
}

fun Date.toCalendar(): Calendar {
    val calendar = Calendar.getInstance()
    calendar.setTime(this)
    return calendar
}

fun Calendar.isInTTLockPasscodeRange(startDate: LocalDateTime): Boolean {
    return toDate().isInTTLockPasscodeRange(startDate)
}

fun Calendar.nextTTLockPasscode(start: Calendar): Calendar {
    return toDate().nextTTLockPasscode(start.toDate()).toCalendar()
}

fun Calendar.toDate(): LocalDateTime {
    return toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime()
}

fun LocalDateTime.isInTTLockPasscodeRange(startDate: LocalDateTime): Boolean {
    val startYear = startDate.year
    val startMonth = startDate.monthValue
    val startDay = startDate.dayOfMonth
    val startHour = startDate.hour
    val startMinute = startDate.minute

    val yearValid = when {
        year == startYear && monthValue == startMonth && dayOfMonth >= startDay -> true
        year == startYear && monthValue > startMonth -> true
        year == startYear + 1 && monthValue < startMonth -> true
        year == startYear + 1 && monthValue == startMonth && dayOfMonth <= startDay && hour <= startHour && minute <= startMinute -> true
        year > startYear + 1 && monthValue == startMonth && dayOfMonth == startDay && hour == startHour && minute == startMinute -> true
        else -> false
    }
    return yearValid
}

fun LocalDateTime.nextTTLockPasscode(start: LocalDateTime): LocalDateTime {
    return LocalDateTime.of(year, start.monthValue, start.dayOfMonth, start.hour, start.minute)
}

fun LocalDateTime.toCalendar(): Calendar {
    val zonedDateTime: ZonedDateTime = atZone(ZoneId.systemDefault())
    val instant: Instant? = zonedDateTime.toInstant()
    val date = Date.from(instant)
    val calendar = Calendar.getInstance()
    calendar.setTime(date)

    return calendar
}