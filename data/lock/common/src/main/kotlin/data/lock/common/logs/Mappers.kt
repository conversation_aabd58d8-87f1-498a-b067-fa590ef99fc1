package data.lock.common.logs

import core.locks.logs.models.LockLog
import data.database.schema.LockLogEntity

internal fun LockLogEntity.toModel(): LockLog {
    return LockLog(
        localId = id,
        actionType = action_type,
        lockInfoData = lock_info_data,
        lockInternalId = lock_internal_id,
        message = message,
        timeStampDateInMillis = timestamp_date,
        provider = provider,
        user = user
    )
}