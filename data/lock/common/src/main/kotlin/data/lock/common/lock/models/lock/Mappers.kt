package data.lock.common.lock.models.lock

import data.database.schema.IconEntity
import data.database.schema.LockAssignmentEntity
import data.database.schema.LockEntity
import data.database.schema.LockPropertyEntity
import data.database.schema.PropertyEntity
import data.database.schema.TimeProfileIdEntity
import data.database.schema.TimeRangeEntity
import data.database.schema.UserHomeResponseEntity

fun TimeProfileIdEntity.toModel(): TimeProfileId {
    return TimeProfileId(
        id = id,
        name = name,
        iseoId = iseo_id,
        status = status,
        createdAt = created_at,
        createdBy = created_by
    )
}

fun TimeRangeEntity.toModel(): TimeRange {
    return TimeRange(
        id = id,
        allowedDays = allowed_days,
        alwaysOpen = always_open,
        createdAt = created_at,
        holidays = holidays,
        name = name,
        routineId = routine_id,
        status = status,
        startHour = start_hour.toInt(),
        startMin = start_minute.toInt(),
        endHour = end_hour.toInt(),
        endMin = end_minute.toInt()
    )
}

fun IconEntity.toModel(): Icon {
    return Icon(
        id = id,
        name = name,
        icon = icon,
        createdAt = created_at,
        type = type
    )
}

fun PropertyEntity.toModel(icons: List<Icon>): Property {
    return Property(
        latitude = latitude,
        longitude = longitude,
        managerId = manager_id,
        managerType = manager_type,
        emirate = emirate,
        area = area,
        buildingName = building_name,
        totalFloors = total_floors,
        createdAt = created_at,
        iconId = icon_id,
        supportCallNumber = support_call_number,
        supportWhatsappNumber = support_whatsapp_number,
        icons = icons,
        count = count.toInt(),
        id = id,
        laundryNumber = laundry_number,
        groceryNumber = grocery_number,
        maintenanceNumber = maintenance_number
    )
}

fun LockPropertyEntity.toModel(): PropertyDetails {
    return PropertyDetails(
        latitude = latitude,
        longitude = longitude,
        managerId = manager_id,
        managerType = manager_type,
        emirate = emirate,
        area = area,
        buildingName = building_name,
        totalFloors = total_floors,
        createdAt = created_at,
        iconId = icon_id,
        supportCallNumber = support_call_number,
        supportWhatsappNumber = support_whatsapp_number,
        count = count.toInt(),
        id = id,
        floor = floor,
        name = name,
        roomNumber = room_number,
        laundryNumber = laundry_number,
        groceryNumber = grocery_number,
        maintenanceNumber = maintenance_number,
        apartmentNumber = apartment_number,
        mapId = map_id
    )
}

fun LockAssignmentEntity.toModel(timeProfileId: TimeProfileId, timeRanges: List<TimeRange>): LockAssignment {
    return LockAssignment(
        id = id,
        assignedAt = assigned_at,
        assignedBy = assigned_by,
        assignedTo = assigned_to,
        lockId = lock_id,
        timeProfileId = timeProfileId,
        validFrom = valid_from,
        validTo = valid_to,
        status = status.toInt(),
        timeRanges = timeRanges
    )
}

fun LockEntity.toModel(
    assignment: LockAssignment,
    icons: List<Icon>,
    property: PropertyDetails
): Lock {
    return Lock(
        assignment = assignment,
        ownerId = owner_id,
        unitId = unit_id,
        bookingNumber = booking_number,
        privacy = privacy,
        privacyChanged = privacy_changed,
        companyCheckIn = company_check_in,
        checkIn = check_in,
        totalCheckins = total_checkins.toInt(),
        accessKey = access_key,
        batteryLevel = battery_level.toInt(),
        createdAt = created_at,
        image = image,
        lockUid = lock_uid,
        name = name,
        desc = desc,
        provider = provider,
        status = status.toInt(),
        uniqueKey = unique_key,
        timeZone = time_zone,
        internalId = internal_id,
        encryptedKey = encrypted_key,
        privacyMode = privacy_mode,
        primary = primary_lock,
        privacyPermission = privacy_permission,
        privacyOwner = privacy_owner,
        icons = icons,
        property = property,
        id = id,
        firmwareUpdated = firmware_updated,
        passcodeId = passcode_id,
        passcode = passcode,
        firmwareVersion = firmware_version,
        firmwareAvailableVersion = firmware_available_version,
        tedeeLockId = tedee_lock_id
    )
}

fun UserHomeResponseEntity.toModel(
    locks: List<Lock>,
    properties: List<Property>
): UserHomeResponse {
    return UserHomeResponse(
        locks = locks,
        properties = properties,
        success = success,
        assignmentId = assignment_id,
        message = message,
        isPaid = is_paid,
        logout = logout,
        totalUnreadNotification = total_unread_notification.toInt()
    )
}