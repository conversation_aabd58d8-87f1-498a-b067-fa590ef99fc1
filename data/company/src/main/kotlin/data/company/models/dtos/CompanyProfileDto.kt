package data.company.models.dtos

import data.company.models.CompanyProfile
import kotlinx.serialization.Serializable

@Serializable
internal class CompanyProfileResponseDto(
    val data: CompanyProfileDto
)

@Serializable
internal data class CompanyProfileDto(
    val _id: String? = null,
    val status: Long? = null,
    val country_code: String? = null,
    val timezone: String = "",
    val timezone_name: String = "",
    val company_name: String? = null,
    val business_lia: String = "",
    val url: String? = null,
    val first_name: String? = null,
    val last_name: String? = null,
    val address: String? = null,
    val city: String? = null,
    val email_id: String? = null,
    val mobile_number: Long? = null,
    val user_id: String? = null,
    val total_licenses: Long? = null,
    val card_details: String? = null,
    val created_at: String? = null,
    val __v: Long? = null,
    val updated_at: String? = null,
    val plan_id: String? = null,
    val trn_number: String = "",
    val business_type: String = "",
    val trade_license_number: String = "",
    val country: String? = null,
    val zip_code: String? = null,
    val checkin: Boolean = false
) {
    fun toModel(): CompanyProfile {
        return CompanyProfile(
            id = _id ?: "",
            status = status,
            timezoneOffset = timezone,
            timezoneName = timezone_name,
            companyName = company_name,
            businessLia = business_lia,
            url = url,
            address = address ?: "",
            city = city ?: "",
            userId = user_id,
            totalLicenses = total_licenses,
            cardDetails = card_details,
            createdAt = created_at,
            version = __v,
            updatedAt = updated_at,
            planId = plan_id,
            trnNumber = trn_number,
            businessType = business_type,
            tradeLicenseNumber = trade_license_number,
            country = country,
            zipCode = zip_code,
            checkIn = checkin
        )
    }
}