name: "Upload artifact"
description: "Upload artifact"
inputs:
  shell:
    required: true
    description: "Shell to use depending on runner os"

  uploadName:
    required: true
    description: "Upload name"
  uploadPath:
    required: true
    description: "Upload path"

outputs:
  fileName:
    description: "Upload file name"
    value: ${{ steps.logName.outputs.fileName }}
  filePath:
    description: "Upload file path"
    value: ${{ steps.logPath.outputs.filePath }}

runs:
  using: "composite"
  steps:
    - name: Upload artifact
      id: uploadArtifact
      uses: actions/upload-artifact@v4
      with:
        name: ${{ inputs.uploadName }}
        path: ${{ inputs.uploadPath }}

    - name: Log path
      id: logPath
      run: |
        RESULT=$(dirname ${{ inputs.uploadPath }})
        echo result: $RESULT
        echo "filePath=$RESULT" >> $GITHUB_OUTPUT
      shell: ${{ inputs.shell }}

    - name: Log name
      id: logName
      run: |
        RESULT=$(basename ${{ inputs.uploadPath }})
        echo result: $RESULT
        echo "fileName=$RESULT" >> $GITHUB_OUTPUT
      shell: ${{ inputs.shell }}