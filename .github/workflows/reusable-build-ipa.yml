name: Build ipa

on:
  workflow_call:
    inputs:
      environment:
        required: true
        description: "Environment to build for"
        type: string
      versionName:
        required: true
        description: "Version name to use"
        type: string

      artifactUploadIpaName:
        required: true
        description: "name of artifact upload"
        type: string
      artifactUploadDSYMName:
        required: true
        description: "name of artifact upload"
        type: string

    outputs:
      ipaPath:
        description: "Path to the ipa"
        value: ${{ jobs.build.outputs.ipaPath }}
      ipaName:
        description: "Name of the ipa"
        value: ${{ jobs.build.outputs.ipaName }}
      dsymPath:
        description: "Path to the DSYM"
        value: ${{ jobs.build.outputs.dsymPath }}
      dsymName:
        description: "Name of the DSYM"
        value: ${{ jobs.build.outputs.dsymName }}
jobs:
  build:
    name: Build
    environment:
      name: ${{ inputs.environment }}
    runs-on: macos-latest
    permissions:
      actions: read
      contents: read
      deployments: write
    outputs:
      ipaPath: ${{ steps.uploadIpa.outputs.filePath }}
      ipaName: ${{ steps.uploadIpa.outputs.fileName }}
      dsymPath: ${{ steps.uploadDSYM.outputs.filePath }}
      dsymName: ${{ steps.uploadDSYM.outputs.fileName }}
    steps:
      - name: Checkout
        uses: actions/checkout@v3
        with:
          token: ${{ secrets.SUBMODULE_ACCESS_TOKEN }}
          submodules: true

      - name: Inject secrets
        uses: ./.github/actions/job/secrets
        with:
          environment: ${{ inputs.environment }}

          keylessBaseApiUrl: ${{ secrets.BASE_API_URL }}
          keylessBaseImageUrl: ${{ secrets.BASE_IMAGE_URL }}
          keylessForgotPasswordUrl: ${{ secrets.FORGOT_PASSWORD_URL }}

          releaseSigningKeyAlias: ${{ secrets.KEYLESS_ANDROID_SIGNING_ALIAS }}
          releaseSigningKeyPassword: ${{ secrets.KEYLESS_ANDROID_SIGNING_PASSWORD }}
          releaseSigningKeyStoreFile: "app/release.jks"
          releaseSigningKeyStoreFileContent: ${{ secrets.KEYLESS_ANDROID_SIGNING_KEYSTORE }}
          releaseSigningKeyStorePassword: ${{ secrets.KEYLESS_ANDROID_SIGNING_KEYSTORE_PASSWORD }}

          shell: ${{ env.shell }}

      - name: Inject android google service
        uses: ./.github/actions/job/file/create
        with:
          filePath: app/google-services.json
          fileContent: ${{ secrets.ANDROID_FIREBASE_GOOGLE_SERVICE }}

      - name: Init JDK
        uses: ./.github/actions/job/setup-jdk

      - name: Init cache
        uses: ./.github/actions/job/setup-cache

      - name: Init gradle
        uses: ./.github/actions/gradle/setup-gradle

      - name: Set up ruby env
        uses: ruby/setup-ruby@v1.138.0
        with:
          ruby-version: 3.2.1
          bundler-cache: true
          working-directory: ./ios

      - name: Init version name
        id: init-version-name
        uses: ./.github/actions/job/release-version
        with:
          refName: ${{ inputs.versionName }}
          shell: ${{ env.shell }}

      - name: Decode signing certificate into a file
        working-directory: ./ios
        env:
          CERTIFICATE_BASE64: ${{ secrets.KEYLESS_IOS_DIST_SIGNING_KEY }}
          PROFILE_BASE64: ${{ secrets.KEYLESS_IOS_PROVISIONING_PROFILE }}
          FIREBASE_DISTRIBUTION_SERVICE: ${{ secrets.KEYLESS_FIREBASE_DISTRIBUTION_SERVICE }}
        run: |
          echo $CERTIFICATE_BASE64 | base64 --decode > signing-cert.p12
          echo $PROFILE_BASE64 | base64 --decode > profile.mobileprovision
          
          mkdir -p ~/Library/MobileDevice/Provisioning\ Profiles
          cp profile.mobileprovision ~/Library/MobileDevice/Provisioning\ Profiles
          
          echo ${FIREBASE_DISTRIBUTION_SERVICE} >> firebase-distribution-service.json

      - name: Build ipa
        working-directory: ./ios
        run: bundle exec fastlane ios build_ipa
        env:
          ASC_KEY_ID: ${{ secrets.KEYLESS_ASC_KEY_ID }}
          ASC_ISSUER_ID: ${{ secrets.KEYLESS_ASC_ISSUER_ID }}
          ASC_KEY: ${{ secrets.KEYLESS_ASC_PRIVATE_KEY }}
          SIGNING_KEY_PASSWORD: ${{ secrets.KEYLESS_IOS_DIST_SIGNING_KEY_PASSWORD }}
          BUILD_APP_IDENTIFIER: ${{ secrets.KEYLESS_BUILD_APP_IDENTIFIER }}
          IOS_DISTRIBUTION_PROFILE: ${{ secrets.KEYLESS_IOS_DISTRIBUTION_PROFILE }}
          SIGNING_KEY_FILE_PATH: signing-cert.p12
          VERSION_NAME: ${{ steps.init-version-name.outputs.versionName }}

      - name: Upload built ipa
        id: uploadIpa
        uses: ./.github/actions/job/artifact-upload
        with:
          uploadName: ${{ inputs.artifactUploadIpaName }}
          uploadPath: ios/keyless-ios.ipa
          shell: ${{ env.shell }}

      - name: Upload built dsym
        id: uploadDSYM
        uses: ./.github/actions/job/artifact-upload
        with:
          uploadName: ${{ inputs.artifactUploadDSYMName }}
          uploadPath: ios/keyless-ios.app.dSYM.zip
          shell: ${{ env.shell }}