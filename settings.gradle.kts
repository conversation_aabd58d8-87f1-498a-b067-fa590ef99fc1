pluginManagement {
    includeBuild(
        if (rootProject.name == "KeylessMobileLogic") {
            "build-logic"
        } else {
            "mobilelogic/build-logic"
        }
    )
    repositories {
        gradlePluginPortal()
        google()
        mavenCentral()
        maven("https://jitpack.io")
        jcenter()
    }
}

dependencyResolutionManagement {
    repositoriesMode.set(RepositoriesMode.PREFER_SETTINGS)
    repositories {
        google()
        mavenCentral()
        maven("https://maven.regulaforensics.com/RegulaDocumentReader")
        maven("https://jitpack.io")
        jcenter()
    }
}
rootProject.name = "KeylessMobileLogic"

private data class ProjectDeclaration(val name: String, val path: String = name.replace("-", "/"))

private val projects: List<ProjectDeclaration> = listOf(
    ProjectDeclaration("core-common"),
    ProjectDeclaration("core-encryption"),
    ProjectDeclaration("core-project"),

    ProjectDeclaration("core-caching-key-value", "core/caching/key-value"),
    ProjectDeclaration("core-caching-key-value-test", "core/caching/key-value-test"),

    ProjectDeclaration("core-http-client-common"),
    ProjectDeclaration("core-http-client-ktor"),
    ProjectDeclaration("core-http-client-test"),
    ProjectDeclaration("core-http-download-common"),
    ProjectDeclaration("core-http-download-test"),

    ProjectDeclaration("core-monitoring-common"),
    ProjectDeclaration("core-monitoring-firebase"),
    ProjectDeclaration("core-monitoring-test"),

    ProjectDeclaration("core-lock-airbnk"),
    ProjectDeclaration("core-lock-airbnk-sdk", "core/lock/airbnk-sdk"),
    ProjectDeclaration("core-lock-common"),
    ProjectDeclaration("core-lock-iseo"),
    ProjectDeclaration("core-lock-iseo-sdk", "core/lock/iseo-sdk"),
    ProjectDeclaration("core-lock-logs", "core/lock/logs"),
    ProjectDeclaration("core-locks-manager", "core/lock/manager"),
    ProjectDeclaration("core-lock-mst"),
    ProjectDeclaration("core-lock-mst-sdk", "core/lock/mst-sdk"),
    ProjectDeclaration("core-lock-nordicfirmware"),
    ProjectDeclaration("core-lock-rayonics"),
    ProjectDeclaration("core-lock-tedee"),
    ProjectDeclaration("core-lock-ttlock"),
    ProjectDeclaration("core-lock-ttlock-sdk", "core/lock/ttlock-sdk"),

    ProjectDeclaration("core-location-common"),
    ProjectDeclaration("core-location-common-test", "core/location/common-test"),
    ProjectDeclaration("core-location-google"),

    ProjectDeclaration("core-permissions-manager"),
    ProjectDeclaration("core-permissions-manager-test", "core/permissions/manager-test"),

    ProjectDeclaration("core-regula-common"),
    ProjectDeclaration("core-regula-documents"),
    ProjectDeclaration("core-regula-face")
)


projects.forEach { project ->
    include(":${project.name}")
    project(":${project.name}").projectDir = file(project.path)
}