package domain.regula

import core.common.status.StatusRepository
import core.monitoring.common.repository.Logger
import core.regula.documents.RegulaDocuments
import core.regula.documents.RegulaFace
import data.user.account.repositories.UserAccountRepository
import domain.regula.models.CheckInEvent
import domain.regula.models.Event
import domain.regula.models.ScreenEvent
import domain.regula.repositories.ScreenStateRepository
import domain.regula.repositories.SideEffectsRepository
import domain.regula.usecases.RegulaUseCases

class ViewModel(
    status: StatusRepository,
    logger: Logger,
    documents: RegulaDocuments,
    face: RegulaFace,
    user: UserAccountRepository
) {

    private val screen = ScreenStateRepository(
        status = status
    )
    private val sideEffects = SideEffectsRepository()
    private val useCases = RegulaUseCases(
        screen = screen,
        sideEffects = sideEffects,
        documents = documents,
        user = user,
        face = face,
        status = status,
        logger = logger
    )

    val screenStream = screen.stream
    val sideEffectsStream = sideEffects.stream

    suspend fun onEvent(event: Event) = when (event) {
        is ScreenEvent -> onScreenEvent(event)
        is CheckInEvent -> onCheckInEvent(event)
    }

    private suspend fun onScreenEvent(event: ScreenEvent): Unit = when (event) {
        is ScreenEvent.SelectDocumentType -> {
            useCases.screen.selectDocumentType(event)
        }

        is ScreenEvent.ScanFrontSide -> {
            useCases.scan.scanFrontSide(event)
        }

        is ScreenEvent.ScanBackSide -> {
            useCases.scan.scanBackSide(event)
        }

        is ScreenEvent.ScanFace -> {
            useCases.face.scanFace(event)
        }

        is ScreenEvent.InitFaceScreen -> {
            useCases.screen.initFaceScreen(event)
        }

        is ScreenEvent.DocumentsNextClick -> {
            useCases.screen.documentsNextClick(event)
        }

        is ScreenEvent.FaceNextClick -> {
            useCases.screen.faceNextClick(event)
        }

        is ScreenEvent.InitDocumentScreen -> {
            useCases.screen.initDocumentScreen(event)
        }

        is ScreenEvent.EndFaceScreen -> {
            useCases.screen.endFaceScreen(event)
        }

        is ScreenEvent.EndDocumentScreen -> {
            useCases.screen.endDocumentScreen()
        }
    }

    fun deInitRegula() {
        useCases.screen.endDocumentScreen()
    }

    private suspend fun onCheckInEvent(event: CheckInEvent): Unit = when (event) {
        is CheckInEvent.ConfirmCheckIn -> {
            useCases.checkIn.checkIn(event)
        }
    }
}