package domain.settings.company.staff.add.usecases

import core.common.status.StatusRepository
import core.monitoring.common.repository.Logger
import core.monitoring.common.repository.log
import domain.settings.company.staff.add.models.ScreenEvent
import domain.settings.company.staff.add.repositories.ScreenStateRepository
import domain.settings.company.staff.add.repositories.SideEffectsRepository

internal class UIUseCases(
    private val screen: ScreenStateRepository,
    private val sideEffects: SideEffectsRepository,
    private val logger: Logger,
    private val status: StatusRepository
) {

    fun selectRole(event: ScreenEvent.SelectRole) = logger.log {
        screen.update {
            it.copy(
                selectedRole = event.role
            )
        }
    }
}