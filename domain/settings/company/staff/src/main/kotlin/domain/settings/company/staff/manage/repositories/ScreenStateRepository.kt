package domain.settings.company.staff.manage.repositories

import domain.settings.company.staff.manage.models.ScreenState
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.flow.update

class ScreenStateRepository {
    private val state = MutableStateFlow(emptyState())
    private var screenState = state.value
    val stream = combination(
        screenState = state
    ) { screen ->
        screen.copy()
    }.onEach { screenState = it }

    fun update(block: (ScreenState) -> ScreenState) {
        state.update { block(it) }
    }
}