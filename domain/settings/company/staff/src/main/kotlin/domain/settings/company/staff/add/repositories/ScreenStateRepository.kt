package domain.settings.company.staff.add.repositories

import core.common.status.StatusRepository
import domain.settings.company.staff.add.models.ScreenState
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.update

class ScreenStateRepository(
    private val status: StatusRepository
) {

    private val state = MutableStateFlow(emptyState())

    val stream = combination(
        screenState = state,
        status = status.stream
    ) { screen, status ->
        screen.copy(
            status = status
        )
    }

    fun update(block: (ScreenState) -> ScreenState) {
        state.update { block(it) }
    }
}