package data.settings.company.repositories

import data.settings.company.models.ScreenState
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.combine

internal inline fun ScreenStateRepository.combination(
    screenState: Flow<ScreenState>,
    crossinline transform: suspend (
        screenState: ScreenState
    ) -> ScreenState
): Flow<ScreenState> {
    return combine(
        screenState
    ) { args: Array<*> ->
        transform(
            args[0] as ScreenState
        )
    }
}

internal fun emptyState(): ScreenState {
    return ScreenState(
        lias = emptyList(),
        companyProfile = null
    )
}