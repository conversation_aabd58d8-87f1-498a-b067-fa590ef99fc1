package domain.settings.support

import core.common.status.StatusRepository
import core.locks.logs.repostiories.LockLogsRepository
import core.locks.manager.LocksManager
import core.monitoring.common.repository.Logger
import core.permissions.manager.BluetoothManager
import core.permissions.manager.PermissionsManager
import domain.settings.support.models.DiagnosticEvent
import domain.settings.support.models.Event
import domain.settings.support.models.ScreenEvent
import domain.settings.support.repositories.ScreenStateRepository
import domain.settings.support.repositories.SideEffectsRepository
import domain.settings.support.usecases.SupportUseCases

class ViewModel(
    lockLogs: LockLogsRepository,
    manager: LocksManager,
    bluetooth: BluetoothManager,
    permissions: PermissionsManager,
    logger: Logger,
    status: StatusRepository
) {
    private val screenState = ScreenStateRepository(status = status)
    private val sideEffects = SideEffectsRepository()
    private val useCases = SupportUseCases(
        lockLogs,
        logger = logger,
        status = status,
        screenRepository = screenState,
        sideEffects = sideEffects,
        manager = manager,
        bluetooth = bluetooth,
        permissions = permissions
    )
    val screenStream = screenState.stream
    val sideEffectsStream = sideEffects.stream

    suspend fun onEvent(event: Event) = when (event) {
        is DiagnosticEvent -> onDiagnosticEvent(event)
        is ScreenEvent -> onScreenEvent(event)
    }

    private suspend fun onDiagnosticEvent(event: DiagnosticEvent) = when (event) {
        is DiagnosticEvent.SendLogs -> useCases.sendLockLogs.execute(event)
    }

    private suspend fun onScreenEvent(event: ScreenEvent) = when (event) {
        is ScreenEvent.EndNearBy -> useCases.screen.endNearBy()
        is ScreenEvent.InitNearBy -> useCases.screen.initNearBy()
        is ScreenEvent.PauseNearBy -> useCases.nearby.pause(event)
        is ScreenEvent.ResumeNearBy -> useCases.nearby.resume(event)
    }
}